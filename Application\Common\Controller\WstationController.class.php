<?php

namespace Common\Controller;

use Think\Controller;

/**
 * 商户后台管理控制
 */
class WstationController extends BaseController
{
    public $userRow = [];
    public $mod = 1; // 1 直接授权， 2 代理授权
    public $conf = [];

    /**
     * 初始化方法
     */
    public function _initialize()
    {
        $this->setConf([
            'number' => '2',
            'WX_ID' => 'wxbe5ef2f3c4081630',
            'WX_TOKEN' => 'zcgk123567',
            'WX_APPID' => 'wxbe5ef2f3c4081630',
            'WX_APPSECRET' => 'd31659831cf47f6fab621e7141ddb61a',
        ]);

        if (C('LOCAL_DEBUG')) {
            $user = D('User')->find(68);
            session('openid2', $user['openid']);
            session('wx.user_id2', $user['id']);
            return;
        }
        if (!isWX()) {
            header("Content-type: text/html; charset=utf-8");
            die('请通过微信使用此功能 :)');
        }

        // 设置微信分享菜单隐藏功能
        $this->setupWechatShareHiding();
    }

    /**
     * 设置微信分享菜单隐藏功能
     * 自动为所有站点模块页面配置微信JSSDK以隐藏分享菜单
     */
    protected function setupWechatShareHiding()
    {
        // 只在微信环境中且非调试模式下运行
        if (!isWX() || C('LOCAL_DEBUG')) {
            return;
        }

        try {
            // 获取当前页面URL
            $protocol = "https://";
            $currentUrl = $protocol . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
            $currentUrl = preg_replace('/#.*$/', '', $currentUrl); // 移除锚点

            // 生成微信JSSDK配置
            $wxconf = getWxConfig($currentUrl, $this->conf);

            if ($wxconf) {
                // 分配微信配置到视图
                $this->assign('wechatConfig', $wxconf);
                $this->assign('wechatAppId', $this->conf['WX_APPID']);

                // 生成隐藏分享菜单的JavaScript代码
                $hideShareScript = $this->generateHideShareScript($wxconf, $this->conf['WX_APPID']);
                $this->assign('wechatHideShareScript', $hideShareScript);
            }
        } catch (Exception $e) {
            // 静默处理错误，不影响页面正常功能
            \Think\Log::write('WeChat share hiding setup failed: ' . $e->getMessage(), 'WARN');
        }
    }

    /**
     * 检查招就办禁用状态
     * @param int $serviceStationId 服务站ID
     * @return bool true=已禁用，false=正常
     */
    protected function checkZsbDisabled($serviceStationId)
    {
        if (!$serviceStationId) {
            return false;
        }

        $serviceStation = D("ServiceStation")->where([
            'id' => $serviceStationId,
            'zsb_type' => 2
        ])->field('is_disabled')->find();

        return $serviceStation ? (bool)$serviceStation['is_disabled'] : false;
    }

    /**
     * 生成隐藏微信分享菜单的JavaScript代码
     * @param array $wxconf 微信配置
     * @param string $appId 微信AppID
     * @return string JavaScript代码
     */
    protected function generateHideShareScript($wxconf, $appId)
    {
        $timestamp = isset($wxconf['timestamp']) ? $wxconf['timestamp'] : 0;
        $nonceStr = isset($wxconf['noncestr']) ? $wxconf['noncestr'] : '';
        $signature = isset($wxconf['signature']) ? $wxconf['signature'] : '';

        return "
<script>
// 微信分享菜单隐藏功能 - 自动应用于所有站点模块页面
(function() {
    // 检测是否在微信环境中
    var isWechat = /MicroMessenger/i.test(navigator.userAgent);

    if (!isWechat || typeof wx === 'undefined') {
        return; // 非微信环境或JSSDK未加载，直接返回
    }

    try {
        // 配置微信JSSDK
        wx.config({
            debug: false,
            appId: '{$appId}',
            timestamp: {$timestamp},
            nonceStr: '{$nonceStr}',
            signature: '{$signature}',
            jsApiList: [
                'hideMenuItems',
                'hideOptionMenu',
                'showMenuItems'
            ]
        });

        wx.ready(function() {
            try {
                // 隐藏微信分享相关菜单项
                wx.hideMenuItems({
                    menuList: [
                        'menuItem:share:appMessage',    // 发送给朋友
                        'menuItem:share:timeline',      // 分享到朋友圈
                        'menuItem:share:qq',            // 分享到QQ
                        'menuItem:share:weiboApp',      // 分享到微博
                        'menuItem:share:QZone',         // 分享到QQ空间
                        'menuItem:share:facebook'       // 分享到Facebook
                    ],
                    success: function() {
                        console.log('微信分享菜单已隐藏');
                    },
                    fail: function(err) {
                        console.warn('隐藏微信分享菜单失败:', err);
                    }
                });
            } catch (error) {
                console.warn('微信分享菜单隐藏功能执行失败:', error);
            }
        });

        wx.error(function(res) {
            console.warn('微信JSSDK配置失败:', res);
        });

    } catch (error) {
        console.warn('微信分享菜单隐藏功能初始化失败:', error);
    }
})();
</script>";
    }

    public function setConf($conf)
    {
        $this->conf = $conf;
    }

    /**
     * 微信code方式授权，获取 openid , scope:snsapi_base
     */
    public function codeAuth($conf = [])
    {
        if (!$conf) {
            $conf = $this->conf;
        }
        $suf = isset($conf['number']) ? $conf['number'] : '';
        if (!$this->authed($suf)) {
            vendor('LaneWeChat.lanewechat');
            if (!$code = I("get.code")) {
                \LaneWeChat\Core\Base::init($conf);
                $redirect_uri = preg_replace('/code=\w+\&?/', '', __HOST__ . $_SERVER['REQUEST_URI']);
                $redirect_uri = preg_replace('/\&?state=\w+\&?/', '', $redirect_uri);
                $state = (int)$conf['number'] > 10 ? $conf['number'] : (int)$conf['number'] + 1;
                if ($this->mod == 2) {
                    $state = base64_encode($redirect_uri);
                    $c_dm = D('Conf')->C('SYS_OUTER_DM_AUTH');
                    \LaneWeChat\Core\WeChatOAuth::getCode(proxy_uri($c_dm), $state);
                } else if ($this->mod == 1) {
                    \LaneWeChat\Core\WeChatOAuth::getCode($redirect_uri, $state);
                }
            } else {
                $row = [];
                \LaneWeChat\Core\Base::init($conf);
                unset($_GET['code']);
                $res = \LaneWeChat\Core\WeChatOAuth::getAccessTokenAndOpenId($code);
                if ($res['errcode']) {
                    dolog('error/wx/authcode', "code:$code, result:" . json_encode($res) . ' ' . (int)$conf['number'] . $_SERVER['HTTP_USER_AGENT']);
                    die('Failed to get openid, errcode:' . $res['errcode']); // 授权失败,记录日志
                } else {
                    $num = $conf['number'] > 10 ? 9 : $conf['number'];
                    if ($num == 9) {
                        $str = 'openid' . $num;
                        session($str, $res['openid']);
                    } else {
                        $str = 'openid2';
                        session($str, $res['openid']);
                    }
                }
            }
        }
        return true;
    }

    /**
     * 微信code方式授权，scope:snsapi_userinfo
     */
    public function codeAuth2()
    {
        if (!$this->loggedIn()) {
            vendor('LaneWeChat.lanewechat');
            $conf = $this->conf;
            $url = __HOST__ . $_SERVER['REQUEST_URI'];
            $code = $_REQUEST['code'];

            if (!empty($_REQUEST['code']) && cookie('curPath')) {
                unset($_REQUEST['code']);
                $url = cookie('curPath');
                session('auth2code', $code);
                $code = false;
                cookie('curPath', null);
            }
            \LaneWeChat\Core\Base::init($conf);
            if (!$code) {
                if ($this->mod == 2) {
                    $state = base64_encode($url);
                    \LaneWeChat\Core\WeChatOAuth::getCode(proxy_uri(D('Conf')->C('SYS_OUTER_DM_AUTH')), $state, 'snsapi_userinfo');
                } else if ($this->mod == 1) {
                    \LaneWeChat\Core\WeChatOAuth::getCode($url, '1', 'snsapi_userinfo');
                }
                exit;
            } else {
                unset($_GET['code']);
                //dolog('wx/authcode', "referer:{$_SERVER["HTTP_REFERER"]}, return data:".json_encode(I("get.")));
                $res = \LaneWeChat\Core\WeChatOAuth::getAccessTokenAndOpenId($code);

                if ($res['errcode']) {
                    dolog('error/wx/authcode3', "result:" . json_encode($res));
                } else {
                    session('openid', $res['openid']);
                    D("User")->init($res['openid'], $res['access_token'], 2); // 初始化用户表
                }
            }
            noCodeJump();

        }
    }

    /**
     * 微信端授权登录
     * @return bool
     */
    public function login($auth = true)
    {
        if ($this->loggedIn()) return true;
        if (!I("get.code")) {
            cookie('curPath', __HOST__ . $_SERVER['REQUEST_URI'], 60);
        }
        $res = $this->codeAuth();
        if ($res) {
            $user = D("User")->where(['openid' => session('openid')])->find();
            // 目前只需授权一次，获取用户信息
            if ($user && !empty($user['nickname'])) {
                session('wx.user_id', $user['id']);
                return true;
            } else {
                if ($auth === true) $this->codeAuth2();
            }
        }
    }

    /**
     * 是否已授权
     */
    public function authed()
    {
        return session('openid2') ? true : false;
    }

    /**
     * 是否已登录
     */
    public function loggedIn()
    {
        return session('wx.user_id2') ? true : false;
    }

    /**
     * 登出，清除session
     */
    public function logout()
    {
        session(null);
    }
}

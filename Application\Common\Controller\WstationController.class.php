<?php

namespace Common\Controller;

use Think\Controller;

/**
 * 商户后台管理控制
 */
class WstationController extends BaseController
{
    public $userRow = [];
    public $mod = 1; // 1 直接授权， 2 代理授权
    public $conf = [];

    /**
     * 初始化方法
     */
    public function _initialize()
    {
        $this->setConf([
            'number' => '2',
            'WX_ID' => 'wxbe5ef2f3c4081630',
            'WX_TOKEN' => 'zcgk123567',
            'WX_APPID' => 'wxbe5ef2f3c4081630',
            'WX_APPSECRET' => 'd31659831cf47f6fab621e7141ddb61a',
        ]);

        if (C('LOCAL_DEBUG')) {
            $user = D('User')->find(68);
            session('openid2', $user['openid']);
            session('wx.user_id2', $user['id']);
            // 即使在调试模式下也设置微信分享隐藏功能
            $this->setupWechatShareHiding();
            return;
        }
        if (!isWX()) {
            header("Content-type: text/html; charset=utf-8");
            die('请通过微信使用此功能 :)');
        }

        // 设置微信分享菜单隐藏功能
        $this->setupWechatShareHiding();
    }

    /**
     * 设置微信分享菜单隐藏功能
     * 自动为所有站点模块页面配置微信JSSDK以隐藏分享菜单
     */
    protected function setupWechatShareHiding()
    {
        // 在调试模式下，模拟微信环境进行测试
        if (C('LOCAL_DEBUG')) {
            // 调试模式下强制启用，用于测试
            \Think\Log::write('WeChat share hiding - 调试模式启用', 'INFO');
        } else {
            // 生产模式下只在微信环境中运行
            if (!isWX()) {
                return;
            }
        }

        try {
            // 获取当前页面URL
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https://" : "http://";
            $currentUrl = $protocol . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
            $currentUrl = preg_replace('/#.*$/', '', $currentUrl); // 移除锚点

            // 记录调试信息
            \Think\Log::write('WeChat share hiding - URL: ' . $currentUrl, 'INFO');
            \Think\Log::write('WeChat share hiding - Config: ' . json_encode($this->conf), 'INFO');

            // 生成微信JSSDK配置
            $wxconf = getWxConfig($currentUrl, $this->conf);

            // 记录配置结果
            \Think\Log::write('WeChat share hiding - wxconf result: ' . json_encode($wxconf), 'INFO');

            // 无论是否有有效配置，都生成隐藏脚本（使用备用方案）
            $hideShareScript = $this->generateHideShareScript($wxconf, $this->conf['WX_APPID'], $currentUrl);
            $this->assign('wechatHideShareScript', $hideShareScript);

            if ($wxconf) {
                // 分配微信配置到视图
                $this->assign('wechatConfig', $wxconf);
                $this->assign('wechatAppId', $this->conf['WX_APPID']);
            }
        } catch (Exception $e) {
            // 记录详细错误信息
            \Think\Log::write('WeChat share hiding setup failed: ' . $e->getMessage() . ' Trace: ' . $e->getTraceAsString(), 'ERROR');

            // 即使出错也提供基础的隐藏脚本
            $basicHideScript = $this->generateBasicHideShareScript();
            $this->assign('wechatHideShareScript', $basicHideScript);
        }
    }

    /**
     * 生成基础的分享菜单隐藏脚本（备用方案）
     * 当JSSDK配置失败时使用
     * @return string JavaScript代码
     */
    protected function generateBasicHideShareScript()
    {
        return "
<script>
// 微信分享菜单隐藏功能 - 基础版（备用方案）
(function() {
    console.log('WeChat Share Hiding: 使用基础隐藏方案');

    // 检测是否在微信环境中
    var isWechat = /MicroMessenger/i.test(navigator.userAgent);

    if (!isWechat) {
        return;
    }

    // 方法1: CSS隐藏分享按钮（如果页面有的话）
    var style = document.createElement('style');
    style.innerHTML = `
        /* 隐藏可能的分享按钮 */
        .share-btn, .share-button, [class*='share'], [id*='share'] {
            display: none !important;
        }
        /* 隐藏微信内置分享相关元素 */
        .wx_menu, .wx-menu, [class*='wx-share'], [id*='wx-share'] {
            display: none !important;
        }
    `;
    document.head.appendChild(style);

    // 方法2: 尝试使用简单的wx配置（如果可用）
    function tryBasicWxConfig() {
        if (typeof wx !== 'undefined') {
            try {
                // 尝试隐藏选项菜单
                wx.hideOptionMenu && wx.hideOptionMenu();
                console.log('WeChat Share Hiding: 基础方案 - 选项菜单已隐藏');
            } catch (e) {
                console.log('WeChat Share Hiding: 基础方案 - wx方法不可用');
            }
        }
    }

    // 页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', tryBasicWxConfig);
    } else {
        tryBasicWxConfig();
    }

    // 延迟执行，确保微信JSSDK加载
    setTimeout(tryBasicWxConfig, 1000);
    setTimeout(tryBasicWxConfig, 3000);

    console.log('WeChat Share Hiding: 基础隐藏方案已应用');
})();
</script>";
    }

    /**
     * 检查招就办禁用状态
     * @param int $serviceStationId 服务站ID
     * @return bool true=已禁用，false=正常
     */
    protected function checkZsbDisabled($serviceStationId)
    {
        if (!$serviceStationId) {
            return false;
        }

        $serviceStation = D("ServiceStation")->where([
            'id' => $serviceStationId,
            'zsb_type' => 2
        ])->field('is_disabled')->find();

        return $serviceStation ? (bool)$serviceStation['is_disabled'] : false;
    }

    /**
     * 生成隐藏微信分享菜单的JavaScript代码
     * @param array $wxconf 微信配置
     * @param string $appId 微信AppID
     * @param string $currentUrl 当前页面URL
     * @return string JavaScript代码
     */
    protected function generateHideShareScript($wxconf, $appId, $currentUrl = '')
    {
        $timestamp = isset($wxconf['timestamp']) ? $wxconf['timestamp'] : 0;
        $nonceStr = isset($wxconf['noncestr']) ? $wxconf['noncestr'] : '';
        $signature = isset($wxconf['signature']) ? $wxconf['signature'] : '';

        // 如果没有有效配置，使用基础隐藏方案
        if (!$wxconf || !$signature) {
            return $this->generateBasicHideShareScript();
        }

        return "
<script>
// 微信分享菜单隐藏功能 - 增强版
(function() {
    console.log('WeChat Share Hiding: 开始初始化');

    // 检测是否在微信环境中
    var isWechat = /MicroMessenger/i.test(navigator.userAgent);
    console.log('WeChat Share Hiding: 微信环境检测 =', isWechat);

    if (!isWechat) {
        console.log('WeChat Share Hiding: 非微信环境，跳过');
        return;
    }

    // 等待微信JSSDK加载
    function waitForWx() {
        if (typeof wx === 'undefined') {
            console.log('WeChat Share Hiding: 等待微信JSSDK加载...');
            setTimeout(waitForWx, 100);
            return;
        }

        console.log('WeChat Share Hiding: 微信JSSDK已加载，开始配置');
        initWechatConfig();
    }

    function initWechatConfig() {
        try {
            // 配置微信JSSDK
            wx.config({
                debug: true, // 临时开启调试模式
                appId: '{$appId}',
                timestamp: {$timestamp},
                nonceStr: '{$nonceStr}',
                signature: '{$signature}',
                jsApiList: [
                    'hideMenuItems',
                    'hideOptionMenu',
                    'showMenuItems',
                    'onMenuShareAppMessage',
                    'onMenuShareTimeline'
                ]
            });

            console.log('WeChat Share Hiding: wx.config 已调用');

            wx.ready(function() {
                console.log('WeChat Share Hiding: wx.ready 回调执行');

                try {
                    // 方法1: 隐藏特定菜单项
                    wx.hideMenuItems({
                        menuList: [
                            'menuItem:share:appMessage',    // 发送给朋友
                            'menuItem:share:timeline',      // 分享到朋友圈
                            'menuItem:share:qq',            // 分享到QQ
                            'menuItem:share:weiboApp',      // 分享到微博
                            'menuItem:share:QZone',         // 分享到QQ空间
                            'menuItem:share:facebook'       // 分享到Facebook
                        ],
                        success: function() {
                            console.log('WeChat Share Hiding: 分享菜单隐藏成功');
                        },
                        fail: function(err) {
                            console.warn('WeChat Share Hiding: 隐藏分享菜单失败:', err);
                            // 尝试方法2: 隐藏整个选项菜单
                            tryHideOptionMenu();
                        }
                    });

                    // 方法3: 覆盖分享回调
                    wx.onMenuShareAppMessage({
                        title: '分享已禁用',
                        desc: '此页面不允许分享',
                        link: window.location.href,
                        imgUrl: '',
                        success: function() {
                            console.log('WeChat Share Hiding: 分享被阻止');
                            return false;
                        },
                        cancel: function() {
                            console.log('WeChat Share Hiding: 用户取消分享');
                        }
                    });

                    wx.onMenuShareTimeline({
                        title: '分享已禁用',
                        link: window.location.href,
                        imgUrl: '',
                        success: function() {
                            console.log('WeChat Share Hiding: 朋友圈分享被阻止');
                            return false;
                        },
                        cancel: function() {
                            console.log('WeChat Share Hiding: 用户取消朋友圈分享');
                        }
                    });

                } catch (error) {
                    console.error('WeChat Share Hiding: 执行失败:', error);
                }
            });

            wx.error(function(res) {
                console.error('WeChat Share Hiding: JSSDK配置失败:', res);
                console.log('WeChat Share Hiding: 当前URL:', window.location.href);
                console.log('WeChat Share Hiding: 配置信息:', {
                    appId: '{$appId}',
                    timestamp: {$timestamp},
                    nonceStr: '{$nonceStr}',
                    signature: '{$signature}'
                });
            });

        } catch (error) {
            console.error('WeChat Share Hiding: 初始化失败:', error);
        }
    }

    function tryHideOptionMenu() {
        try {
            wx.hideOptionMenu();
            console.log('WeChat Share Hiding: 整个选项菜单已隐藏');
        } catch (error) {
            console.error('WeChat Share Hiding: 隐藏选项菜单失败:', error);
        }
    }

    // 开始等待微信JSSDK
    waitForWx();
})();
</script>";
    }

    public function setConf($conf)
    {
        $this->conf = $conf;
    }

    /**
     * 微信code方式授权，获取 openid , scope:snsapi_base
     */
    public function codeAuth($conf = [])
    {
        if (!$conf) {
            $conf = $this->conf;
        }
        $suf = isset($conf['number']) ? $conf['number'] : '';
        if (!$this->authed($suf)) {
            vendor('LaneWeChat.lanewechat');
            if (!$code = I("get.code")) {
                \LaneWeChat\Core\Base::init($conf);
                $redirect_uri = preg_replace('/code=\w+\&?/', '', __HOST__ . $_SERVER['REQUEST_URI']);
                $redirect_uri = preg_replace('/\&?state=\w+\&?/', '', $redirect_uri);
                $state = (int)$conf['number'] > 10 ? $conf['number'] : (int)$conf['number'] + 1;
                if ($this->mod == 2) {
                    $state = base64_encode($redirect_uri);
                    $c_dm = D('Conf')->C('SYS_OUTER_DM_AUTH');
                    \LaneWeChat\Core\WeChatOAuth::getCode(proxy_uri($c_dm), $state);
                } else if ($this->mod == 1) {
                    \LaneWeChat\Core\WeChatOAuth::getCode($redirect_uri, $state);
                }
            } else {
                $row = [];
                \LaneWeChat\Core\Base::init($conf);
                unset($_GET['code']);
                $res = \LaneWeChat\Core\WeChatOAuth::getAccessTokenAndOpenId($code);
                if ($res['errcode']) {
                    dolog('error/wx/authcode', "code:$code, result:" . json_encode($res) . ' ' . (int)$conf['number'] . $_SERVER['HTTP_USER_AGENT']);
                    die('Failed to get openid, errcode:' . $res['errcode']); // 授权失败,记录日志
                } else {
                    $num = $conf['number'] > 10 ? 9 : $conf['number'];
                    if ($num == 9) {
                        $str = 'openid' . $num;
                        session($str, $res['openid']);
                    } else {
                        $str = 'openid2';
                        session($str, $res['openid']);
                    }
                }
            }
        }
        return true;
    }

    /**
     * 微信code方式授权，scope:snsapi_userinfo
     */
    public function codeAuth2()
    {
        if (!$this->loggedIn()) {
            vendor('LaneWeChat.lanewechat');
            $conf = $this->conf;
            $url = __HOST__ . $_SERVER['REQUEST_URI'];
            $code = $_REQUEST['code'];

            if (!empty($_REQUEST['code']) && cookie('curPath')) {
                unset($_REQUEST['code']);
                $url = cookie('curPath');
                session('auth2code', $code);
                $code = false;
                cookie('curPath', null);
            }
            \LaneWeChat\Core\Base::init($conf);
            if (!$code) {
                if ($this->mod == 2) {
                    $state = base64_encode($url);
                    \LaneWeChat\Core\WeChatOAuth::getCode(proxy_uri(D('Conf')->C('SYS_OUTER_DM_AUTH')), $state, 'snsapi_userinfo');
                } else if ($this->mod == 1) {
                    \LaneWeChat\Core\WeChatOAuth::getCode($url, '1', 'snsapi_userinfo');
                }
                exit;
            } else {
                unset($_GET['code']);
                //dolog('wx/authcode', "referer:{$_SERVER["HTTP_REFERER"]}, return data:".json_encode(I("get.")));
                $res = \LaneWeChat\Core\WeChatOAuth::getAccessTokenAndOpenId($code);

                if ($res['errcode']) {
                    dolog('error/wx/authcode3', "result:" . json_encode($res));
                } else {
                    session('openid', $res['openid']);
                    D("User")->init($res['openid'], $res['access_token'], 2); // 初始化用户表
                }
            }
            noCodeJump();

        }
    }

    /**
     * 微信端授权登录
     * @return bool
     */
    public function login($auth = true)
    {
        if ($this->loggedIn()) return true;
        if (!I("get.code")) {
            cookie('curPath', __HOST__ . $_SERVER['REQUEST_URI'], 60);
        }
        $res = $this->codeAuth();
        if ($res) {
            $user = D("User")->where(['openid' => session('openid')])->find();
            // 目前只需授权一次，获取用户信息
            if ($user && !empty($user['nickname'])) {
                session('wx.user_id', $user['id']);
                return true;
            } else {
                if ($auth === true) $this->codeAuth2();
            }
        }
    }

    /**
     * 是否已授权
     */
    public function authed()
    {
        return session('openid2') ? true : false;
    }

    /**
     * 是否已登录
     */
    public function loggedIn()
    {
        return session('wx.user_id2') ? true : false;
    }

    /**
     * 登出，清除session
     */
    public function logout()
    {
        session(null);
    }
}

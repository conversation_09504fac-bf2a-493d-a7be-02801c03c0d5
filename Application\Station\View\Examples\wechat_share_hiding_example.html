<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>WeChat Share Hiding Example</title>
    
    <!-- Required: Include WeChat JSSDK -->
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            background: #f5f5f5;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            color: #999;
            font-size: 14px;
        }
        .code {
            background: #eee;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            overflow-x: auto;
        }
        .note {
            background: #fffde7;
            padding: 10px;
            border-left: 4px solid #ffd600;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>WeChat Share Hiding Example</h1>
            <p>This page demonstrates the automatic WeChat share menu hiding functionality.</p>
        </div>
        
        <div class="section">
            <h2>How It Works</h2>
            <p>When this page is viewed in a WeChat browser, the share menu options in the top-right menu will be automatically hidden.</p>
            <p>This functionality is implemented at the controller level in <code>WstationController</code> and applies to all pages automatically.</p>
        </div>
        
        <div class="section">
            <h2>Implementation</h2>
            <p>The share hiding functionality is automatically applied through the base controller. No additional code is required in controllers.</p>
            
            <h3>View Integration</h3>
            <p>To ensure the share hiding script is included in your view, add this code before the closing body tag:</p>
            <div class="code">
                &lt;!-- WeChat Share Menu Hiding - Automatic Application --&gt;<br>
                &lt;if condition="isset($wechatHideShareScript)"&gt;<br>
                {$wechatHideShareScript}<br>
                &lt;/if&gt;
            </div>
        </div>
        
        <div class="section">
            <h2>Testing</h2>
            <p>To test this functionality:</p>
            <ol>
                <li>Open this page in a WeChat browser</li>
                <li>Tap the "..." menu in the top-right corner</li>
                <li>Verify that share options are hidden</li>
            </ol>
            
            <div class="note">
                <strong>Note:</strong> This feature only works in actual WeChat browsers, not in desktop browsers or other mobile browsers.
            </div>
        </div>
        
        <div class="section">
            <h2>Browser Detection</h2>
            <p>Current browser detection:</p>
            <div id="browser-info"></div>
            
            <script>
                document.getElementById('browser-info').innerHTML = 
                    'Is WeChat: ' + (/MicroMessenger/i.test(navigator.userAgent) ? 'Yes' : 'No') + '<br>' +
                    'User Agent: ' + navigator.userAgent;
            </script>
        </div>
        
        <div class="footer">
            <p>Station Module - WeChat Share Hiding Feature Example</p>
        </div>
    </div>
    
    <!-- WeChat Share Menu Hiding - Automatic Application -->
    <if condition="isset($wechatHideShareScript)">
    {$wechatHideShareScript}
    </if>
    
    <!-- Manual implementation example (for demonstration only) -->
    <script>
    // This is just for demonstration - the automatic implementation above is preferred
    (function() {
        // Check if in WeChat browser
        var isWechat = /MicroMessenger/i.test(navigator.userAgent);
        
        if (!isWechat || typeof wx === 'undefined') {
            console.log('Not in WeChat browser or JSSDK not loaded');
            return;
        }
        
        // Log WeChat environment detection
        console.log('WeChat browser detected');
        
        // The actual implementation is handled by the automatically generated script
    })();
    </script>
</body>
</html>

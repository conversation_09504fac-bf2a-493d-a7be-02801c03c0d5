<!DOCTYPE html>
<html >
<head>
<title>管理中心</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta content="no-cache" http-equiv="pragma">
<meta content="0" http-equiv="expires">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
<link rel="stylesheet" type="text/css" href="/static/stations/css/css.css?v={:time()}" media="all">
<link rel="stylesheet" type="text/css" href="/static/stations/css/swiper-bundle.css" media="all">
<link rel="stylesheet" type="text/css" href="/static/stations/css/iconfont.css" media="all">
<script type="text/javascript" src="/static/stations/js/jquery-1.9.1.min.js"></script>
<script type="text/javascript" src="/static/stations/js/swiper-bundle.min.js"></script>
<script src="/static/js/layer/layer.m.js"></script>
<!-- 微信JSSDK -->
<script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>

<style>


    /* 添加按钮样式 */
    .add-btn {
        margin-top: 2px;
        padding: 10px 18px;
        background: #FF6B35;
        color: white;
        border: none;
        border-radius: 10px;
        font-size: 17px;
        cursor: pointer;
        transition: all 0.2s;
        box-shadow: 0 4px 12px rgba(0,191,128,0.3);
    }

    .add-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0,191,128,0.4);
    }

    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        justify-content: center;
        align-items: center;
        z-index: 999;
    }

    /* 大图样式 */
    .modal-img {
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
    }

    /* 缩略图样式 */
    .thumbnail {
        width: 200px;
        height: 200px;
        cursor: pointer;
        object-fit: cover;
    }

    /* 模态框遮罩层 */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.9);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    /* 模态框内容 */
    .modal-content {
        background: white;
        padding: 28px;
        border-radius: 12px;
        width: 90%;
        max-width: 480px;
        position: relative;
        animation: modalSlide 0.4s cubic-bezier(0.23, 1, 0.32, 1);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    @keyframes modalSlide {
        from {
            transform: scale(0.8);
            opacity: 0;
        }
        to {
            transform: scale(1);
            opacity: 1;
        }
    }

    /* 操作类型选择 */
    .method-section {
        margin-bottom: 24px;
    }

    .method-title {
        color: #1a1a1a;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        text-align: center;
    }

    .method-options {
        display: grid;
        gap: 20px;
    }

    /* 上传区域 */
    .upload-box {
        border: 2px dashed #00bf80;
        border-radius: 12px;
        padding: 24px;
        text-align: center;
        position: relative;
        transition: all 0.3s;
    }

    .upload-box:hover {
        background: rgba(0,191,128,0.05);
    }

    #file-input {
        position: absolute;
        opacity: 0;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        cursor: pointer;
    }

    .upload-icon {
        font-size: 40px;
        margin-bottom: 12px;
        color: #00bf80;
    }

    /* 进度条样式 */
    .progress-bar {
        width: 100%;
        height: 8px;
        background: #eee;
        border-radius: 4px;
        margin-top: 16px;
        overflow: hidden;
        display: none;
    }

    .progress {
        width: 0%;
        height: 100%;
        background: #00bf80;
        transition: width 0.3s ease;
    }

    /* 多阶段进度指示器 */
    .multi-stage-progress {
        display: none;
        margin-top: 20px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 12px;
        border: 1px solid #e9ecef;
    }

    .stage-header {
        text-align: center;
        margin-bottom: 20px;
    }

    .stage-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-bottom: 8px;
    }

    .stage-subtitle {
        font-size: 14px;
        color: #666;
    }

    .stages-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        position: relative;
    }

    .stage-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        position: relative;
        z-index: 2;
    }

    .stage-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        margin-bottom: 8px;
        transition: all 0.3s ease;
        background: #e9ecef;
        color: #6c757d;
        border: 2px solid #e9ecef;
    }

    .stage-icon.pending {
        background: #e9ecef;
        color: #6c757d;
        border-color: #e9ecef;
    }

    .stage-icon.active {
        background: #00bf80;
        color: white;
        border-color: #00bf80;
        animation: pulse 2s infinite;
    }

    .stage-icon.completed {
        background: #28a745;
        color: white;
        border-color: #28a745;
    }

    .stage-icon.error {
        background: #dc3545;
        color: white;
        border-color: #dc3545;
    }

    .stage-label {
        font-size: 12px;
        text-align: center;
        color: #666;
        font-weight: 500;
        max-width: 80px;
        line-height: 1.2;
    }

    .stage-item.active .stage-label {
        color: #00bf80;
        font-weight: bold;
    }

    .stage-item.completed .stage-label {
        color: #28a745;
    }

    .stage-item.error .stage-label {
        color: #dc3545;
    }

    /* 连接线 */
    .stages-container::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 10%;
        right: 10%;
        height: 2px;
        background: #e9ecef;
        z-index: 1;
    }

    .stage-progress-line {
        position: absolute;
        top: 20px;
        left: 10%;
        height: 2px;
        background: #00bf80;
        z-index: 1;
        width: 0%;
        transition: width 0.5s ease;
    }

    /* 脉冲动画 */
    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(0, 191, 128, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(0, 191, 128, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(0, 191, 128, 0);
        }
    }

    /* 旋转动画 */
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .stage-icon.active .icon-spinner {
        animation: spin 1s linear infinite;
    }

    /* 当前状态描述 */
    .current-stage-info {
        text-align: center;
        margin-top: 15px;
        padding: 12px;
        background: white;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .current-stage-text {
        font-size: 14px;
        color: #333;
        margin-bottom: 5px;
    }

    .current-stage-detail {
        font-size: 12px;
        color: #666;
    }

    /* 链接生成区域 */
    .link-section {
        margin-top: 8px;
    }

    /* 按钮组容器 */
    .link-section > div:first-child {
        display: flex;
        gap: 12px;
        margin-bottom: 16px;
        flex-wrap: wrap;
    }

    .link-box {
        display: flex;
        gap: 10px;
        margin-top: 16px;
    }

    .link-input {
        flex: 1;
        padding: 12px 16px;
        border: 1px solid #e1e5e9;
        border-radius: 8px;
        font-size: 14px;
        background: #f8f9fa;
        transition: all 0.2s;
    }

    .link-input:focus {
        outline: none;
        border-color: #00bf80;
        background: white;
    }

    /* 主操作按钮 - 生成链接 */
    .copy-btn {
        padding: 12px 20px;
        background: #00bf80;
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s;
        font-weight: 500;
        font-size: 14px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .copy-btn:hover {
        background: #00a86b;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,191,128,0.3);
    }

    .copy-btn:disabled {
        background: #cccccc !important;
        color: #666666 !important;
        cursor: not-allowed !important;
        transform: none !important;
        box-shadow: none !important;
        opacity: 0.6 !important;
    }

    .copy-btn:disabled:hover {
        background: #cccccc !important;
        transform: none !important;
        box-shadow: none !important;
    }

    /* 次要操作按钮 - 下载模板 */
    .secondary-btn {
        background: #6c757d !important;
        border: 1px solid #6c757d !important;
    }

    .secondary-btn:hover {
        background: #5a6268 !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(108,117,125,0.3);
    }

    /* 关闭按钮 */
    .close-btn {
        position: absolute;
        top: 16px;
        right: 16px;
        width: 28px;
        height: 28px;
        cursor: pointer;
        opacity: 0.6;
        transition: all 0.2s;
        background: rgba(0,0,0,0.05);
        border-radius: 50%;
        padding: 4px;
    }

    .close-btn:hover {
        opacity: 1;
        background: rgba(0,0,0,0.1);
        transform: scale(1.1);
    }

    /* 提示信息 */
    .hint-text {
        color: #6c757d;
        font-size: 13px;
        margin-top: 8px;
        text-align: center;
        line-height: 1.4;
    }

    .success-msg {
        color: #00bf80;
        display: none;
        margin-top: 12px;
        font-weight: 500;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .modal-content {
            width: 95%;
            padding: 20px;
            margin: 20px;
        }

        .link-section > div:first-child {
            flex-direction: column;
            gap: 10px;
        }

        .copy-btn {
            width: 100%;
            justify-content: center;
        }

        .method-title {
            font-size: 16px;
        }
    }
    #specialNotice {
    display: none; /* 默认隐藏声明 */
}
</style>
</head>
<body>
<include file="header"/>

 

    <div class="page0104 mb10" id="specialNotice">
        <div class="weap">
            <div class="boxs">

                <div class="bar01">
                    <div class="title" style="text-align: center;color: red;"">
                        <h3>特别声明</h3>
                    </div>
                </div>
                <div class="bd">
                    <ul class="clearfix" style="font-size: 14px;">
                        <li style="padding-left: 8px;width: 100%;line-height: 25px;color: #000;">
                            <A href="{:U('index/law', ['id' => '13'])}">
                            平台及服务站均需严格遵守国家法律法规开展经营活动。任何服务站不得利用平行合作关系开展诸如非法集资、传销、诈骗等违法违规行为，一经发现，平台将立即终止合作，并依法追究其法律责任 >></A>
                        </li>
                            <li style="width: 100%;text-align: center;margin-top: 6px;">
                            <A href="{:U('index/law', ['id' => '5'])}" style="padding-left: 5px;text-decoration: underline;font-size: 12px;">服务协议</A>
                            <A href="{:U('index/law', ['id' => '6'])}" style="text-decoration: underline;font-size: 12px;">用户协议</A>
                            <A href="{:U('index/law', ['id' => '4'])}" style="text-decoration: underline;font-size: 12px;">隐私政策</A>
                            <A href="{:U('index/law', ['id' => '7'])}" style="text-decoration: underline;font-size: 12px;">隐私保护指引</A>
                        </li>
                    </ul>
                </div>

            </div>


        </div>

    </div>

    <div class="page0103 mb10">
        <div class="weap">
            <div class="boxs">
                <div class="bar01">
                    <div class="title">
                        <h3>常用功能</h3>
                    </div>
                </div>

                                <div class="bd">
                    <ul class="clearfix">
                        <li>
                            <a href="{:U('index/moments')}">
                                <div class="ico"><img src="/static/stations/images/pyq.png" alt=""></div>
                                <h3>图文素材</h3>
                            </a>
                        </li>
                    <li>
                        <a href="https://work.weixin.qq.com/kfid/kfc0f3ecae7d5279996">
                            <div class="ico"><img src="/static/stations/images/ico-b06.png" alt=""></div>
                            <h3>官方客服</h3>
                        </a>
                    </li>
               
                     <li>
                        <a href="{:U('index/qa')}">
                            <div class="ico"><img src="/static/stations/images/aq.png?t=232" alt=""></div>
                            <h3>问答学堂</h3>
                        </a>
                    </li>
                    <li>
                        <a href="{:U('index/allindex')}">
                            <div class="ico"><img src="/static/stations/images/ic_all.png" alt=""></div>
                            <h3>显示详情</h3>
                        </a>
                    </li>
                     <!--li>
                        <a href="{:U('index/allindex')}">
                            <div class="ico"><img src="/static/stations/images/zsb.png" alt=""></div>
                            <h3>添加推广员</h3>
                        </a>
                    </li-->

                    <!--li>
                        <a href="{:U('index/qa')}">
                            <div class="ico"><img src="/static/stations/images/aq.png?t=232" alt=""></div>
                            <h3>问答学堂</h3>
                        </a>
                    </li-->
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="page0107box mb10">
        <div class="page0107">
            <div class="weap">
                <div class="boxs mb10" style="width: 100%;">
               <div class="hd01">
                <div class="search" style="background-color: #f8d7da; border-radius: 4px; text-align: center;">
                 <span style="color: #721c24; font-size: 13px; line-height: 40px; display: block;">岗位信息及培训费根据市场及用工单位情况会随时调整。</span>
                        </div>
                    </div>
                    <div class="hd01">
                        <div class="search">
                          <input class="input js_kwd" name="" type="kwd" placeholder="搜索岗位关键词" style="width: 100%;">
                          <button class="inbtn"><i class="iconfont icon-31sousuo"></i></button>
                        </div>
                    </div>
                    <div class="hd02">
                        <ul >
                            <li class="on" data-type="0">
                                <a>全部学历</a>
                            </li>
                            <li data-type="1">
 				                <a>中专/高中</a>
                            </li>
                            <li data-type="2">
                                <a>大专</a>
                            </li>
                            <li data-type="3">
                                <a>本科</a>
                            </li>
                            <li data-type="4">
                                <a>更高学历</a>
                            </li>

                        </ul>
                    </div>
                    <div class="hd03">
                        <ul >
                            <li class="on" data-type="0">
                                <a>全部</a>
                            </li>
                            <li  data-type="1">
                                <a>央企</a>
                            </li>

                            <li  data-type="2">
                                <a>国企</a>
                            </li>
                            <li  data-type="4">
                                <a>上市</a>
                            </li>
                            <li  data-type="6">
                                <a>出国</a>
                            </li>
                            <li data-type="7">
                                <a>其他</a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="bd">
                    <ul id="boxcontent">
                        <include file="list-index"/>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-overlay" id="modal">
        <div class="modal-content">
            <!-- 关闭按钮 -->
            <svg class="close-btn" viewBox="0 0 24 24" fill="none" stroke="#666" onclick="closeModal()">
                <path d="M18 6L6 18M6 6l12 12" stroke-width="2" stroke-linecap="round"/>
            </svg>
           <!-- 操作方式选择 -->
           <div class="method-section" style="margin-top: 22px;">
            <h2 class="method-title" id="t1">
                <if condition="$serviceStationRow['zsb_type'] eq 2">
                    上传招就办简历
                <else/>
                    选择简历添加方式
                </if>
            </h2>
            <h2 class="method-title" style="display: none;color: red;font-weight: bold;" id="t2">简历诉求</h2>
            <div class="method-options">
                    <!-- 上传区域 -->
                    <div class="upload-box">

                        <div id="stext">
                        <div class="upload-icon">📤</div>
                        <p>【您自己操作】点这里上传简历文件</p>
                        <p class="hint-text">仅支持使用官方标准模板填写的Word简历文档</p>
                        <p class="hint-text" style="color: #ff6b6b; font-size: 12px; margin-top: 5px;">
                          ⚠️ 必须使用官方模板，非标准模板将被拒绝
                        </p>
                        <input type="file" id="file-input"
                               accept=".docx">
                        </div>

                        <div style="width: 100%;display: none;" ID="j_content">
                            <!-- 分割线 -->
                               <textarea
                               id = "job_content"
                               class="link-input"
                               style="width: 100%;height: 88px;"
                               placeholder="在这里输入该简历诉求，比如想去哪里，想找什么工作"></textarea>
                               <!-- 提交按钮绑定事件 -->
                               <button class="copy-btn" onclick="submitResume()" style="text-align: center;margin-top: 18px;">
                                   提 交
                               </button>
                               <BR>
                        </div>

                        <!-- 上传进度 -->
                        <p class="success-msg" id="upload-success">✓ 正在保存简历……</p>
                        <div class="progress-bar">
                            <div class="progress" id="progress"></div>
                        </div>

                        <!-- 多阶段进度指示器 -->
                        <div class="multi-stage-progress" id="multi-stage-progress">
                            <div class="stage-header">
                                <div class="stage-title">简历处理中</div>
                                <div class="stage-subtitle">请耐心等待，我们正在验证您的简历</div>
                            </div>

                            <div class="stages-container">
                                <div class="stage-progress-line" id="stage-progress-line"></div>

                                <div class="stage-item" id="stage-upload">
                                    <div class="stage-icon pending">
                                        <span class="icon-content">📤</span>
                                    </div>
                                    <div class="stage-label">文件上传</div>
                                </div>

                                <div class="stage-item" id="stage-template">
                                    <div class="stage-icon pending">
                                        <span class="icon-content">📋</span>
                                    </div>
                                    <div class="stage-label">模板检测</div>
                                </div>

                                <div class="stage-item" id="stage-verify">
                                    <div class="stage-icon pending">
                                        <span class="icon-content">🔍</span>
                                    </div>
                                    <div class="stage-label">内容验证</div>
                                </div>

                                <div class="stage-item" id="stage-save">
                                    <div class="stage-icon pending">
                                        <span class="icon-content">💾</span>
                                    </div>
                                    <div class="stage-label">保存简历</div>
                                </div>
                            </div>

                            <div class="current-stage-info" id="current-stage-info">
                                <div class="current-stage-text" id="current-stage-text">准备开始处理...</div>
                                <div class="current-stage-detail" id="current-stage-detail">请稍候</div>
                            </div>
                        </div>


                    </div>

                    <!-- 生成链接区域（仅服务站用户显示） -->
                    <if condition="$serviceStationRow['zsb_type'] eq 1">
                    <div id="stext2">
                    <!-- 分割线 -->
                    <div style="text-align: center; color: #999; margin: 10px 0">或 生成简历创建链接复制发给求职者</div>

                    <!-- 生成链接区域 -->
                    <div class="link-section">
                        <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                            <button class="copy-btn" onclick="generateLink()">
                                点这生成简历创建链接
                            </button>
                            <a href="http://c.zhongcaiguoke.com/data/%E5%BA%94%E8%81%98%E4%BA%BA%E5%91%98%E6%8A%A5%E5%90%8D%E8%A1%A8%EF%BC%88%E9%80%9A%E7%94%A8%EF%BC%89.docx"
                               download="应聘人员报名表（通用）.docx"
                               class="copy-btn secondary-btn"
                               style="text-decoration: none; display: inline-block; text-align: center;">
                                📥 下载标准模板
                            </a>
                        </div>
                        <div class="link-box">
                            <input type="text"
                                   class="link-input"
                                   id="link-text"
                                   readonly
                                   placeholder="点上方生成链接发给求职者">
                            <button class="copy-btn" onclick="copyLink()">
                                复制
                            </button>
                        </div>
                    </div>
                </div>
                </if>
                </div>
            </div>
        </div>
    </div>

<!-- 弹窗层 -->
<div class="modal">
    <img class="modal-img">
</div>

<div class="footernav">
<div class="box">
    <ul>
        <!--li>
            <a href="http://www.zhongcaiguoke.com">
                <div class="ico"><span class="iconfont icon-shouye"></span></div>
                <h3>官网</h3>
            </a>
        </li>
        <li>
            <a href="#">
                <div class="ico"><span class="iconfont icon-dingdan"><i></i></span></div>
                <h3>订单</h3>
            </a>
        </li-->
        <li>
            <a href="javascript:void(0);"  onclick="showModal()">
                <button class="add-btn" onclick="showModal()">+ 添加简历</button>
            </a>
        </li>
        <!--li>
            <a href="#">
                <div class="ico"><span class="iconfont icon-xiaoxi"><i class="iconfont icon-caidan"></i></span></div>
                <h3>消息</h3>
            </a>
        </li>
        <li class="on">
            <a href="#">
                <div class="ico"><span class="iconfont icon-gerenzhongxin"><i></i></span></div>
                <h3>管理中心</h3>
            </a>
        </li-->
    </ul>
</div>
</div>
<div id="copy" style="display: none;"></div>

<script>
    // 显示模态框
    function showModal() {
        document.getElementById('modal').style.display = 'flex';
        document.body.style.overflow = 'hidden'; // 禁止背景滚动
    }

    // 关闭弹窗时重置状态
    function closeModal() {
        document.getElementById('modal').style.display = 'none';
        document.body.style.overflow = 'auto';
        resetUploadStatus();
        // 新增重置操作
        selectedFile = null;
        document.getElementById('job_content').value = '';
    }

    // 点击遮罩关闭
    document.getElementById('modal').addEventListener('click', function(e) {
        if(e.target === this) closeModal();
    });

    // 生成随机链接
    function generateLink() {
        $.ajax({
            type: 'post',
            data: {},
            url: '/index/getsorturl',
            beforeSend: function(){
                //loading层
                layer.open({
                    type: 2,
                    shadeClose : false,
                });
            },
            complete: function(){

                // Handle the complete event
            },
            success: function(data) {
                layer.closeAll();
                ajaxsend = true;
                if (data.status == 1) {
                    $('#link-text').val(data.info);
                }  else {
                    layer.open({
                        style : "width:80%",
                        shadeClose : false,
                        content: "<div style='text-align: left'>"+data.info+"</div>",
                        btn: ['我知道了'],
                        yes: function (index) {
                            layer.closeAll();
                        },
                    });
                }
            }
        });
    }

    // 复制链接功能
    function copyLink() {
        const link = document.getElementById('link-text').value;
        if(!link) return;
        copyToClipboardLegacyTwo(link)

        // navigator.clipboard.writeText(link).then(() => {
        //     showTempMessage('链接已复制到剪贴板');
        // });
    }

 // 新增全局变量用于存储文件对象
let selectedFile = null;

// 修改文件选择事件（仅保存文件不立即上传）
document.getElementById('file-input').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if(!file) return;

    // 验证文件格式
    const fileName = file.name.toLowerCase();
    const validExtensions = ['.doc', '.docx', '.pdf'];
    const isValidFile = validExtensions.some(ext => fileName.endsWith(ext));

    if (!isValidFile) {
        alert('仅支持上传word文档或PDF格式简历');
        this.value = ''; // 清空选择
        return;
    }

    // 保存文件对象
    selectedFile = file;

    // 立即显示输入区域
    const jcontentstr = document.getElementById('j_content');
    const stextstr = document.getElementById('stext');
    const stextstr2 = document.getElementById('stext2');
    jcontentstr.style.display = 'block';
    stextstr.style.display = 'none';
    // 只有服务站用户才隐藏stext2
    <if condition="$serviceStationRow['zsb_type'] eq 1">
    if (stextstr2) stextstr2.style.display = 'none';
    </if>
    document.getElementById('t1').style.display = 'none';
    document.getElementById('t2').style.display = 'block';
});

// 多阶段进度控制
const stages = [
    { id: 'stage-upload', name: '文件上传', icon: '📤', spinner: '⟳' },
    { id: 'stage-template', name: '模板检测', icon: '📋', spinner: '⟳' },
    { id: 'stage-verify', name: '内容验证', icon: '🔍', spinner: '⟳' },
    { id: 'stage-save', name: '保存简历', icon: '💾', spinner: '⟳' }
];

let currentStageIndex = 0;

// 时间记录系统
let resumeUploadTimestamps = {
    processStart: null,
    stageStart: {},
    stageEnd: {},
    processEnd: null
};

// 阶段显示控制
const STAGE_MIN_DURATION = 1000; // 每个阶段最小显示时间（毫秒）
let errorModalClosed = false; // 错误弹窗是否已被用户关闭

// 格式化时间输出函数
function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleTimeString() + '.' + date.getMilliseconds().toString().padStart(3, '0');
}

// 记录阶段时间的函数
function logStageTime(action, stageIndex = null, additionalInfo = '') {
    const timestamp = Date.now();
    const timeStr = formatTimestamp(timestamp);

    if (action === 'process_start') {
        resumeUploadTimestamps.processStart = timestamp;
    } else if (action === 'stage_start') {
        resumeUploadTimestamps.stageStart[stageIndex] = timestamp;
    } else if (action === 'stage_end') {
        resumeUploadTimestamps.stageEnd[stageIndex] = timestamp;
    } else if (action === 'stage_error') {
        // 记录错误时间
    } else if (action === 'process_end') {
        resumeUploadTimestamps.processEnd = timestamp;
    }
}

function showMultiStageProgress() {
    // 重置状态变量
    errorModalClosed = false;

    // 禁用提交按钮，防止重复提交
    const submitBtn = document.querySelector('.copy-btn');
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.style.opacity = '0.6';
        submitBtn.style.cursor = 'not-allowed';
        submitBtn.textContent = '处理中...';
    }

    // 记录进度开始时间
    logStageTime('process_start', null, '- 显示多阶段进度界面');

    document.querySelector('.progress-bar').style.display = 'none';
    document.getElementById('upload-success').style.display = 'none';
    document.getElementById('multi-stage-progress').style.display = 'block';
    currentStageIndex = 0;
    updateStageProgress();
}

function hideMultiStageProgress() {
    document.getElementById('multi-stage-progress').style.display = 'none';

    // 重新启用提交按钮
    const submitBtn = document.querySelector('.copy-btn');
    if (submitBtn) {
        submitBtn.disabled = false;
        submitBtn.style.opacity = '1';
        submitBtn.style.cursor = 'pointer';
        submitBtn.textContent = '提 交';
    }
}

function updateStageProgress() {
    // 记录当前阶段开始时间
    logStageTime('stage_start', currentStageIndex, `- 更新UI界面`);

    const progressLine = document.getElementById('stage-progress-line');
    const currentStageText = document.getElementById('current-stage-text');
    const currentStageDetail = document.getElementById('current-stage-detail');

    const progressPercent = (currentStageIndex / (stages.length - 1)) * 80;
    progressLine.style.width = progressPercent + '%';

    stages.forEach((stage, index) => {
        const stageElement = document.getElementById(stage.id);
        const iconElement = stageElement.querySelector('.stage-icon');
        const iconContent = stageElement.querySelector('.icon-content');

        iconElement.classList.remove('pending', 'active', 'completed', 'error');
        stageElement.classList.remove('pending', 'active', 'completed', 'error');

        if (index < currentStageIndex) {
            iconElement.classList.add('completed');
            stageElement.classList.add('completed');
            iconContent.textContent = '✓';
        } else if (index === currentStageIndex) {
            iconElement.classList.add('active');
            stageElement.classList.add('active');
            iconContent.innerHTML = '<span class="icon-spinner">' + stage.spinner + '</span>';
        } else {
            iconElement.classList.add('pending');
            stageElement.classList.add('pending');
            iconContent.textContent = stage.icon;
        }
    });

    if (currentStageIndex < stages.length) {
        const currentStage = stages[currentStageIndex];
        currentStageText.textContent = currentStage.name + '中...';

        switch (currentStageIndex) {
            case 0:
                currentStageDetail.textContent = '正在上传文件到服务器';
                break;
            case 1:
                currentStageDetail.textContent = '检查简历是否使用官方模板';
                break;
            case 2:
                currentStageDetail.textContent = '验证联系方式和身份信息';
                break;
            case 3:
                currentStageDetail.textContent = '保存简历信息到数据库';
                break;
        }
    }
}

function nextStage() {
    if (currentStageIndex < stages.length - 1) {
        // 记录当前阶段完成时间
        logStageTime('stage_end', currentStageIndex, '- 阶段切换');

        currentStageIndex++;
        updateStageProgress();
    }
}

// 新增：带最小时间控制的阶段切换函数
function nextStageWithMinDuration() {
    return new Promise((resolve) => {
        const stageStartTime = resumeUploadTimestamps.stageStart[currentStageIndex];
        const elapsed = stageStartTime ? Date.now() - stageStartTime : 0;
        const remainingTime = Math.max(0, STAGE_MIN_DURATION - elapsed);

        logStageTime('stage_end', currentStageIndex, `- 阶段切换 (已显示${elapsed}ms, 等待${remainingTime}ms)`);

        setTimeout(() => {
            if (currentStageIndex < stages.length - 1) {
                currentStageIndex++;
                updateStageProgress();
            }
            resolve();
        }, remainingTime);
    });
}

// 新增：处理阶段队列
async function processStageQueue() {
    if (isProcessingQueue) return;
    isProcessingQueue = true;

    while (stageQueue.length > 0) {
        const action = stageQueue.shift();

        if (action.type === 'next_stage') {
            await nextStageWithMinDuration();
        } else if (action.type === 'complete_all') {
            // 快速完成剩余阶段
            while (currentStageIndex < stages.length - 1) {
                await nextStageWithMinDuration();
            }
            // 处理成功完成
            if (serverResponse && serverResponse.status === 1) {
                logStageTime('stage_end', currentStageIndex, '- 最终阶段完成');
                completeAllStages();
                showTempMessage(serverResponse.msg);
                logStageTime('process_end', null, '- 简历上传成功');

                setTimeout(() => {
                    closeModal();
                    window.location.href = serverResponse.url || '{:U("index/joblist")}';
                }, 2000);
            }
        }
    }

    isProcessingQueue = false;
}

function resetToStage(targetStageIndex) {
    // 重置所有阶段状态
    stages.forEach((stage, index) => {
        const stageElement = document.getElementById(stage.id);
        const iconElement = stageElement.querySelector('.stage-icon');
        const iconContent = stageElement.querySelector('.icon-content');

        // 清除所有状态类
        iconElement.classList.remove('active', 'completed', 'error');
        stageElement.classList.remove('active', 'completed', 'error');

        if (index < targetStageIndex) {
            // 前面的阶段设为完成
            iconElement.classList.add('completed');
            stageElement.classList.add('completed');
            iconContent.textContent = '✓';
        } else if (index === targetStageIndex) {
            // 目标阶段设为当前活动阶段
            iconElement.classList.add('active');
            stageElement.classList.add('active');
            iconContent.textContent = stage.spinner;
        } else {
            // 后面的阶段保持默认状态
            iconContent.textContent = stage.icon;
        }
    });

    // 更新当前阶段索引
    currentStageIndex = targetStageIndex;
    // 注意：不调用 updateStageProgress()，避免覆盖错误状态
}

function setStageError(errorMessage) {
    const stageElement = document.getElementById(stages[currentStageIndex].id);
    const iconElement = stageElement.querySelector('.stage-icon');
    const iconContent = stageElement.querySelector('.icon-content');

    // 清除所有状态类，确保错误状态优先
    iconElement.classList.remove('active', 'completed', 'pending');
    stageElement.classList.remove('active', 'completed', 'pending');

    // 设置错误状态
    iconElement.classList.add('error');
    stageElement.classList.add('error');
    iconContent.textContent = '✗';

    document.getElementById('current-stage-text').textContent = '处理失败';
    document.getElementById('current-stage-detail').textContent = errorMessage;
}

function completeAllStages() {
    currentStageIndex = stages.length - 1;
    updateStageProgress();

    setTimeout(() => {
        const lastStageElement = document.getElementById(stages[stages.length - 1].id);
        const iconElement = lastStageElement.querySelector('.stage-icon');
        const iconContent = lastStageElement.querySelector('.icon-content');

        iconElement.classList.remove('active');
        iconElement.classList.add('completed');
        lastStageElement.classList.remove('active');
        lastStageElement.classList.add('completed');
        iconContent.textContent = '✓';

        document.getElementById('current-stage-text').textContent = '处理完成';
        document.getElementById('current-stage-detail').textContent = '简历已成功保存';
    }, 500);
}

// 新增提交函数
function submitResume() {
    if (!selectedFile) {
        alert('请先选择简历文件');
        return;
    }

    const jobContent = document.getElementById('job_content').value.trim();
    if (!jobContent) {
        alert('请输入简历求职诉求');
        return;
    }

    const formData = new FormData();

    // 合并文件和其他参数
    formData.append('file', selectedFile);
    formData.append('jobcontent', jobContent);

    // 显示多阶段进度
    showMultiStageProgress();

    const xhr = new XMLHttpRequest();

    // 添加进度模拟定时器变量
    let progressTimer = null;
    let serverResponseReceived = false;

    xhr.upload.onprogress = function(e) {
        if (e.lengthComputable) {
            const percent = (e.loaded / e.total) * 100;
            if (currentStageIndex === 0 && percent >= 100) {
                setTimeout(() => {
                    nextStage(); // 进入模板检测阶段

                    // 文件上传完成后，不再自动推进
                    // 等待服务器响应来决定后续阶段的推进
                }, 300);
            }
        }
    };

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            // 标记服务器响应已收到
            serverResponseReceived = true;

            // 清除进度模拟定时器
            if (progressTimer) {
                clearTimeout(progressTimer);
                progressTimer = null;
            }

            if (xhr.status === 200) {
                const res = JSON.parse(xhr.responseText);
                if(res.status === 1) {
                    // 成功响应 - 基于后端实际处理状态推进进度
                    // 后端成功意味着所有阶段都已完成，直接推进到最终阶段
                    function progressToFinalStage() {
                        if (currentStageIndex < stages.length - 1) {
                            nextStage();
                            // 每个阶段间隔400ms，快速但清晰地显示进度
                            setTimeout(progressToFinalStage, 400);
                        } else {
                            // 所有阶段完成后的处理
                            setTimeout(() => {
                                completeAllStages();
                                showTempMessage(res.msg);
                                // 2秒后自动关闭弹窗
                                setTimeout(() => {
                                    closeModal();
                                    window.location.href = res.url || '{:U("index/joblist")}'; // TP3.2路由
                                }, 2000);
                            }, 500);
                        }
                    }

                    // 立即开始推进到最终阶段（因为后端已全部处理完成）
                    progressToFinalStage();
                } else if (res.status === 2) {
                    // 重复文件错误，特殊处理

                    // 立即重置到上传阶段显示错误
                    resetToStage(0);
                    setStageError(res.msg);
                    logStageTime('process_end', null, `- 简历上传失败: ${res.msg}`);

                    // 显示友好的重复文件错误弹窗
                    setTimeout(() => {
                        showDuplicateFileError(res);
                    }, 800);
                } else {
                    // 处理错误 - 根据错误类型确定正确的错误阶段并立即显示
                    let errorMessage = res.msg || '处理失败';
                    let targetStageIndex = 1; // 默认为模板检测阶段

                    // 根据错误类型确定正确的错误阶段
                    if (res.error_type === 'verification_failed' ||
                        res.error_type === 'contact_info_missing' ||
                        res.error_type === 'email_missing' ||
                        res.error_type === 'phone_missing' ||
                        res.error_type === 'invalid_id_card' ||
                        res.error_type === 'duplicate_submission' ||
                        res.error_type === 'student_locked') {
                        // 内容验证错误，应该显示在第三阶段
                        targetStageIndex = 2; // 内容验证阶段
                    } else if (res.error_type === 'template_detection_failed') {
                        // 模板检测错误，显示在第二阶段
                        targetStageIndex = 1; // 模板检测阶段
                    } else if (res.error_type === 'upload_failed') {
                        // 上传错误，显示在第一阶段
                        targetStageIndex = 0; // 文件上传阶段
                    }



                    // 立即重置到正确的错误阶段，无论当前进度在哪
                    resetToStage(targetStageIndex);

                    // 立即设置错误状态，不延迟
                    setStageError(errorMessage);

                    // 0.8秒后显示详细错误弹窗，让用户快速看到错误信息
                    setTimeout(() => {
                        showDetailedError(res);
                    }, 800);

                    // 5秒后隐藏进度条并重置状态
                    setTimeout(() => {
                        // 只有在用户没有手动关闭错误弹窗时才自动隐藏
                        if (!errorModalClosed) {
                            hideMultiStageProgress();
                            resetUploadStatus();
                        }
                    }, 5000); // 增加显示时间到5秒，让用户更清楚地看到错误状态
                }
            } else {
                let errorMessage = '网络错误';
                if (xhr.status == 413) {
                    errorMessage = "文件过大，请检查文件大小";
                } else {
                    errorMessage = "网络连接异常，请重试";
                }
                // 网络错误重置到上传阶段
                resetToStage(0);
                setTimeout(() => {
                    setStageError(errorMessage);
                }, 100);
                setTimeout(() => {
                    hideMultiStageProgress();
                    alert("上传失败：" + errorMessage);
                    resetUploadStatus();
                }, 2000);
            }
        }
    };

    xhr.open('POST', '{:U("index/uploadJobFile")}', true);
    xhr.send(formData);
}
    //
    // 显示临时提示信息
    function showTempMessage(msg) {
        const tempDiv = document.createElement('div');
        tempDiv.className = 'hint-text';
        tempDiv.textContent = msg;
        tempDiv.style.color = '#00bf80';
        tempDiv.style.marginTop = '12px';

        document.querySelector('.method-options').appendChild(tempDiv);
        setTimeout(() => tempDiv.remove(), 2000);
    }

    // 显示详细错误信息
    function showDetailedError(response) {

        // 移除之前的错误提示
        const existingError = document.querySelector('.error-detail-modal');
        if (existingError) {
            existingError.remove();
        }

        // 根据错误类型定制错误信息
        let errorIcon = '⚠️';
        let errorTitle = '简历上传失败';
        let suggestions = response.suggestions || [];

        // 优化错误类型判断，支持更精确的错误提示
        if (response.error_type) {
          switch (response.error_type) {
            case 'verification_failed':
              errorIcon = '🔍';
              errorTitle = '简历验证失败';
              suggestions = [
                '请确保上传的是完整的Word文档',
                '检查文件是否损坏或格式错误',
                '如果问题持续，请联系客服'
              ];
              break;
            case 'contact_info_missing':
              errorIcon = '📞';
              errorTitle = '联系方式不完整';
              suggestions = [
                '请在简历中填写有效的手机号码',
                '请在简历中填写有效的邮箱地址',
                '确保联系方式格式正确且清晰可见'
              ];
              break;
          case 'email_missing':
            errorIcon = '📧';
            errorTitle = '邮箱地址缺失';
            suggestions = [
              '请在简历中填写有效的邮箱地址',
              '确保邮箱格式正确（如：<EMAIL>）',
              '邮箱地址应清晰可见，不要使用图片形式'
            ];
            break;
          case 'phone_missing':
            errorIcon = '📱';
            errorTitle = '联系电话缺失';
            suggestions = [
              '请在简历中填写有效的手机号码',
              '确保手机号码为11位数字',
              '联系电话应清晰可见，不要使用图片形式'
            ];
            break;
            case 'invalid_id_card':
              errorIcon = '🆔';
              errorTitle = '身份证信息无效';
              suggestions = [
                '请检查简历中的身份证号码是否正确',
                '确保身份证号码为18位有效数字',
                '检查身份证号码是否清晰可见'
              ];
              break;
            case 'duplicate_submission':
              errorIcon = '🔄';
              errorTitle = '简历重复提交';
              suggestions = [
                '该身份证号码的简历已存在',
                '请检查是否之前已经上传过相同学员的简历',
                '如需更新简历，请先删除之前的记录'
              ];
              break;
            case 'student_locked':
              errorIcon = '🔒';
              errorTitle = '学员已被锁定';
              suggestions = [
                '该学员已在其他服务站注册',
                '如有疑问，请联系相关服务站协调',
                '或联系平台客服处理'
              ];
              break;
            default:
              // 根据错误消息内容智能判断错误类型
              if (response.msg && response.msg.includes('联系')) {
                errorIcon = '📞';
                errorTitle = '联系方式不完整';
                suggestions = [
                  '请在简历中填写有效的手机号码',
                  '请在简历中填写有效的邮箱地址',
                  '确保联系方式格式正确且清晰可见'
                ];
              } else if (response.msg && response.msg.includes('身份证')) {
                errorIcon = '🆔';
                errorTitle = '身份证信息无效';
                suggestions = [
                  '请检查简历中的身份证号码是否正确',
                  '确保身份证号码为18位有效数字',
                  '检查身份证号码是否清晰可见'
                ];
              } else if (response.msg && response.msg.includes('锁定')) {
                errorIcon = '🔒';
                errorTitle = '学员已被锁定';
                suggestions = [
                  '该学员已在其他服务站注册',
                  '如有疑问，请联系相关服务站协调',
                  '或联系平台客服处理'
                ];
              }
              break;
          }
        } else {
          // 如果没有错误类型，根据消息内容智能判断
          if (response.msg && response.msg.includes('联系')) {
            errorIcon = '📞';
            errorTitle = '联系方式不完整';
            suggestions = [
              '请在简历中填写有效的手机号码',
              '请在简历中填写有效的邮箱地址',
              '确保联系方式格式正确且清晰可见'
            ];
          } else if (response.msg && response.msg.includes('身份证')) {
            errorIcon = '🆔';
            errorTitle = '身份证信息无效';
            suggestions = [
              '请检查简历中的身份证号码是否正确',
              '确保身份证号码为18位有效数字',
              '检查身份证号码是否清晰可见'
            ];
          } else if (response.msg && response.msg.includes('锁定')) {
            errorIcon = '🔒';
            errorTitle = '学员已被锁定';
            suggestions = [
              '该学员已在其他服务站注册',
              '如有疑问，请联系相关服务站协调',
              '或联系平台客服处理'
            ];
          }
        }

        // 创建错误详情弹窗
        const errorModal = document.createElement('div');
        errorModal.className = 'error-detail-modal';
        errorModal.innerHTML = `
          <div class="error-detail-content">
            <div class="error-header">
              <span class="error-icon">${errorIcon}</span>
              <h3>${errorTitle}</h3>
              <button class="error-close" onclick="closeErrorModal()">&times;</button>
            </div>
            <div class="error-body">
              <p class="error-message">${response.msg}</p>
              ${suggestions.length > 0 ? `
                <div class="error-suggestions">
                  <h4>💡 解决方案：</h4>
                  <ul>
                    ${suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                  </ul>
                </div>
              ` : ''}
              <div class="error-actions">
                ${response.template_url ? `
                  <a href="${response.template_url}"
                     download="应聘人员报名表（通用）.docx"
                     class="btn-download-template">
                    📥 下载官方模板
                  </a>
                ` : ''}
                <button class="btn-retry" onclick="closeErrorModal()">我知道了</button>
              </div>
            </div>
          </div>
        `;

        document.body.appendChild(errorModal);
        addErrorModalStyles();
    }

    // 添加错误弹窗样式
    function addErrorModalStyles() {
        if (!document.querySelector('#error-modal-styles')) {
            const styles = document.createElement('style');
            styles.id = 'error-modal-styles';
            styles.textContent = `
                .error-detail-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.6);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                    animation: fadeIn 0.3s ease;
                }
                .error-detail-content {
                    background: white;
                    border-radius: 12px;
                    max-width: 500px;
                    width: 90%;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                }
                .error-header {
                    display: flex;
                    align-items: center;
                    padding: 20px;
                    border-bottom: 1px solid #eee;
                    background: #fff5f5;
                    border-radius: 12px 12px 0 0;
                }
                .error-icon {
                    font-size: 24px;
                    margin-right: 10px;
                }
                .error-header h3 {
                    flex: 1;
                    margin: 0;
                    color: #d73027;
                    font-size: 18px;
                }
                .error-close {
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #999;
                    padding: 0;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .error-body {
                    padding: 20px;
                }
                .error-message {
                    font-size: 16px;
                    color: #333;
                    margin-bottom: 15px;
                    line-height: 1.5;
                }
                .error-suggestions {
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 20px;
                }
                .error-suggestions h4 {
                    margin: 0 0 10px 0;
                    color: #495057;
                    font-size: 14px;
                }
                .error-suggestions ul {
                    margin: 0;
                    padding-left: 20px;
                }
                .error-suggestions li {
                    margin-bottom: 8px;
                    color: #6c757d;
                    line-height: 1.4;
                }
                .error-actions {
                    display: flex;
                    gap: 10px;
                    justify-content: flex-end;
                }
                .btn-download-template {
                    background: #28a745;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 6px;
                    text-decoration: none;
                    font-size: 14px;
                    transition: background 0.3s;
                }
                .btn-download-template:hover {
                    background: #218838;
                }
                .btn-retry {
                    background: #007bff;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 14px;
                    transition: background 0.3s;
                }
                .btn-retry:hover {
                    background: #0056b3;
                }
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
            `;
            document.head.appendChild(styles);
        }
    }

    // 显示重复文件错误弹窗
    function showDuplicateFileError(response) {
        // 移除之前的错误提示
        const existingError = document.querySelector('.error-detail-modal');
        if (existingError) {
            existingError.remove();
        }

        // 创建重复文件错误弹窗
        const errorModal = document.createElement('div');
        errorModal.className = 'error-detail-modal';
        errorModal.innerHTML = `
          <div class="error-detail-content">
            <div class="error-header">
              <span class="error-icon">⚠️</span>
              <h3>简历上传失败</h3>
              <button class="error-close" onclick="closeErrorModal()">&times;</button>
            </div>
            <div class="error-body">
              <p class="error-message">${response.msg}</p>
              <div class="error-suggestions">
                <h4>💡 解决方案：</h4>
                <ul>
                  <li>请检查是否之前已经上传过相同的简历文件</li>
                  <li>如需重新上传，请先删除之前的简历记录</li>
                  <li>或者选择其他不同的简历文件进行上传</li>
                </ul>
              </div>
              <div class="error-actions">
                <button class="btn-retry" onclick="closeErrorModal()">我知道了</button>
              </div>
            </div>
          </div>
        `;

        document.body.appendChild(errorModal);
        addErrorModalStyles();
    }

    // 关闭错误弹窗
    function closeErrorModal() {
        const errorModal = document.querySelector('.error-detail-modal');
        if (errorModal) {
            // 标记错误弹窗已被用户关闭
            errorModalClosed = true;

            errorModal.style.animation = 'fadeOut 0.3s ease';
            setTimeout(() => {
                errorModal.remove();

                // 立即隐藏进度条并重置状态，不等待5秒延迟
                hideMultiStageProgress();
                resetUploadStatus();
            }, 300);
        }
    }

    // 重置上传状态
    function resetUploadStatus(delay = 0) {
        setTimeout(() => {
            document.getElementById('file-input').value = '';
            document.getElementById('progress').style.width = '0%';
            document.getElementById('upload-success').style.display = 'none';
            document.getElementById('j_content').style.display = 'none';
            document.getElementById('stext').style.display = 'block';
            document.getElementById('stext2').style.display = 'block';
            document.getElementById('t2').style.display = 'none';
            document.getElementById('t1').style.display = 'block';

            // 重置多阶段进度
            hideMultiStageProgress();
            currentStageIndex = 0;

            // 重置所有阶段状态
            stages.forEach((stage) => {
                const stageElement = document.getElementById(stage.id);
                const iconElement = stageElement.querySelector('.stage-icon');
                const iconContent = stageElement.querySelector('.icon-content');

                iconElement.classList.remove('pending', 'active', 'completed', 'error');
                stageElement.classList.remove('pending', 'active', 'completed', 'error');
                iconElement.classList.add('pending');
                stageElement.classList.add('pending');
                iconContent.textContent = stage.icon;
            });

            // 重置进度线
            document.getElementById('stage-progress-line').style.width = '0%';

            // 重置全局变量
            selectedFile = null;
        }, delay);
    }
</script>
<script>
function removeFirstLineSpaces(text) {
    if (typeof text !== 'string') return text;

    // 核心处理逻辑（新增第五步处理中文括号）
    // 1. 清除首行前导空格
    let processed = text.replace(/^ +/gm, '');

    // 2. 处理开头换行
    const startsWithNewline = /^[\r\n]/.test(text);
    if (startsWithNewline) {
        processed = processed.replace(/^[\r\n]*/, match =>
            match.includes('\r\n') ? '\r\n' : '\n'
        );
    }

    // 3. 处理末尾换行
    processed = processed.replace(/([\r\n])+$/, '$1');

    // 4. 合并连续空行
    processed = processed.replace(/(\r\n|\n){2,}/g, '\n');

    // 5. 新增中文括号换行处理（核心新增逻辑）
    processed = processed.replace(/【/g, '\n\n【')  // 先插入两个换行
        .replace(/(\r\n|\n){3,}/g, '\n\n')        // 处理可能产生的三个以上换行
        .replace(/([\r\n])+$/, '$1');              // 再次确保末尾换行

    return processed;
}

    function copyToClipboardLegacy(text) {
        var texts = removeFirstLineSpaces(text)
        const tempTextArea = document.createElement("textarea");
        tempTextArea.value = texts; // textarea 会保留换行符
        tempTextArea.style.position = "fixed";   // 隐藏元素避免页面闪烁
        tempTextArea.style.left = "-9999px";     // 移出可视区域
        document.body.appendChild(tempTextArea);

        // 2. 选中内容
        tempTextArea.select();
        tempTextArea.setSelectionRange(0, 99999); // 兼容移动端


        // 3. 执行复制命令
        try {
            const success = document.execCommand("copy");
            if (success) {
                layer.open({
                    content: '复制成功！'
                });
            } else {
                layer.open({
                    content: '复制失败！'
                });
            }
        } catch (err) {
            layer.open({
                content: '复制失败！'
            });
        } finally {
            // 4. 清理临时元素
            document.body.removeChild(tempTextArea);
        }
    }

    function copyToClipboardLegacyTwo(text) {
        const tempTextArea = document.createElement("textarea");
        tempTextArea.value = text; // textarea 会保留换行符
        tempTextArea.style.position = "fixed";   // 隐藏元素避免页面闪烁
        tempTextArea.style.left = "-9999px";     // 移出可视区域
        document.body.appendChild(tempTextArea);

        // 2. 选中内容
        tempTextArea.select();
        tempTextArea.setSelectionRange(0, 99999); // 兼容移动端


        // 3. 执行复制命令
        try {
            const success = document.execCommand("copy");
            if (success) {
                layer.open({
                    content: '复制成功！'
                });
            } else {
                layer.open({
                    content: '复制失败！'
                });
            }
        } catch (err) {
            layer.open({
                content: '复制失败！'
            });
        } finally {
            // 4. 清理临时元素
            document.body.removeChild(tempTextArea);
        }
    }

    $("#boxcontent").on('click', '.js_copy', function () {
        let copyid = $(this).attr('data-id');
        let copyhtml =$("#comtepy_"+copyid).html()+"‼️"+$("#texttitle_"+copyid).html()+$("#textContent_"+copyid).html();
        copyToClipboardLegacy(htmlToTextWithLineBreaks(copyhtml.replace(/<\/p>/gi, '<br>').replace(/<p>/gi, '')))
    })

    /**
     * 将 HTML 内容转换为纯文本，处理 <br> 为换行符
     * @param {string} html - 原始 HTML 字符串
     * @returns {string} 转换后的纯文本
     */
    function htmlToTextWithLineBreaks(html) {
        // 创建临时容器
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = html;
        // 处理 <br> 标签为换行符
        const brElements = tempDiv.getElementsByTagName("br");
        while (brElements.length) {
            const br = brElements[0];
            br.parentNode.replaceChild(document.createTextNode("\n"), br);
        }

        // 提取纯文本（自动去除其他标签）
        return tempDiv.textContent || tempDiv.innerText;
    }

    function toggleText(t) {
        const textElement =  document.getElementById('textContent_'+t);
        const arrow = document.querySelector('.arrow');

        textElement.classList.toggle('collapsed');
        textElement.classList.toggle('expanded');

        // 自动滚动到展开位置
        if (textElement.classList.contains('expanded')) {
            textElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }
    // 自动隐藏不需要展开的箭头
    window.addEventListener('DOMContentLoaded', (t) => {
        let textElement = document.getElementById('textContent_'+t);
        let toggleBtn = document.querySelector('.toggle-btn');
        if (textElement != null && textElement.scrollHeight <= textElement.clientHeight) {
            toggleBtn.style.display = 'none';
        }
    });

    var page=1,pages='{:(int)$page->Total_Pages}';
    var type = 0;
    var qualification = 0;
    var kwd = '';
    function ajaxcommon() {
        $.get('/index/index?n=1&p=' + page +'&type='+type+"&qualification="+qualification+"&kwd="+kwd, function(str){
            p = 1;
            $('#boxcontent').html(str);
        }, 'html');
    }
    $(document).ready(function(){

        // 检查招就办价格变化通知弹窗
        // 强制测试弹窗（临时）
        var urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('test_notify') == '1') {
            // 获取当前费率信息用于测试
            $.ajax({
                url: '/index/get_price_change_notify_status',
                type: 'GET',
                data: {},
                dataType: 'json',
                success: function(response) {
                    var ratePercent = response.rate_percent || '30%';
                    showZsbPriceChangeNotifyModal({$serviceStationRow.id}, ratePercent);
                },
                error: function() {
                    showZsbPriceChangeNotifyModal({$serviceStationRow.id}, '30%');
                }
            });
        } else {
            <if condition="$serviceStationRow && $serviceStationRow['zsb_type'] == 2 && $hasNotify">
            checkAndShowZsbPriceChangeNotify();
            </if>
        }

        // 检查localStorage标记，隐藏招就办红点（如果在zjb页面已清除通知）
        checkAndHideZjbNotifyDot();

        // 招就办用户加载余额数据
        <if condition="$serviceStationRow && $serviceStationRow['zsb_type'] == 2">
        loadZjbBalance();
        </if>

    var noticeSwiper = new Swiper('.noticeSwiper', {
        direction: 'vertical',
		loop: true,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
        }
    })
    var noticeSwiper = new Swiper('.page0106Swiper', {
        pagination: {
          el: ".swiper-pagination",
        },
		loop: true,
        autoplay: {
            delay: 3000,
            disableOnInteraction: false,
        }
    });

    $('.js_kwd').blur(function () {
        var  new_kwd = $(this).val();
        if (new_kwd != kwd) {
            $(this).siblings().removeClass("on");
            $(this).addClass("on");
            kwd = new_kwd
            ajaxcommon();
        }
    })


	$(".page0107 .hd02 li").click(function(){
        $(this).siblings().removeClass("on");
       $(this).addClass("on");
   });

   $(".page0107 .hd03 li").click(function(){
       var newType = $(this).attr('data-type');
       if (newType != type) {
           $(this).siblings().removeClass("on");
           $(this).addClass("on");
           type = newType
           ajaxcommon();
       }
   });

    $(".page0107 .hd02 li").click(function(){
        var newQualification = $(this).attr('data-type');
        if (newQualification != qualification) {
            $(this).siblings().removeClass("on");
            $(this).addClass("on");
            qualification = newQualification
            ajaxcommon();
        }
    });

        // 获取元素
        const thumbnail = document.querySelector('.thumbnail');
        const modal = document.querySelector('.modal');
        const modalImg = document.querySelector('.modal-img');
        $("body").on('click', '.thumbnail', function () {
            modal.style.display = 'flex';
            modalImg.src = $(this).attr('src');
        })

        // 点击任意位置关闭弹窗
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });


});
function loadmore(){
    if(page<pages){
        page+=1;
        $.get('/index/index?p=' + page +'&type='+type+"&qualification="+qualification+"&kwd="+kwd, function(str){
            $('#boxcontent').append(str);
        }, 'html');
    } else if (page==pages) {
        page+=1;
        setTimeout("$('.container').append('<p class=\"nomore\">------ 没有了 ------</p>')", 500);
    }
}



$(window).scroll(function(){
    var scrollTop = $(this).scrollTop();     //滚动条距离顶部的高度
    var scrollHeight = $(document).height(); //当前页面的总高度
    var clientHeight = $(this).height();     //当前可视的页面高度

    if(scrollTop + clientHeight >= scrollHeight){
        loadmore();
    }else if(scrollTop<=0){
    }
});


document.addEventListener('DOMContentLoaded', function() {
    // 获取声明元素
    const noticeElement = document.getElementById('specialNotice');

    // 获取当前日期字符串（格式示例："Mon Jul 24 2023"）
    const today = new Date().toDateString();

    // 从本地存储获取最后显示日期
    const lastShownDate = localStorage.getItem('specialNoticeDate');

    // 判断是否是今天第一次访问
    if (!lastShownDate || lastShownDate !== today) {
        // 显示声明
        noticeElement.style.display = 'block';
        // 更新存储日期
        localStorage.setItem('specialNoticeDate', today);
    } else {
        // 隐藏声明
        noticeElement.style.display = 'none';
    }
});

 // 在原有代码中添加
document.querySelector('.close-btn').addEventListener('click', function() {
    document.getElementById('specialNotice').style.display = 'none';
});

// 招就办价格变化通知弹窗相关函数
function checkAndShowZsbPriceChangeNotify() {
    <if condition="$hasNotify">
    var stationId = {$serviceStationRow.id};

    // 显示价格变化通知弹窗
    showZsbPriceChangeNotifyModal(stationId);
    </if>
}

// 显示招就办价格变化通知弹窗
function showZsbPriceChangeNotifyModal(stationId, ratePercent) {
    var notifyDate = new Date();
    var dateStr = notifyDate.getFullYear() + '-' +
                  String(notifyDate.getMonth() + 1).padStart(2, '0') + '-' +
                  String(notifyDate.getDate()).padStart(2, '0') + ' ' +
                  String(notifyDate.getHours()).padStart(2, '0') + ':' +
                  String(notifyDate.getMinutes()).padStart(2, '0');

    // 获取当前费率信息
    var rateText = ratePercent || '请查看价格配置';

    layer.open({
        type: 1,
        title: '平台费率调整通知',
        area: ['90%', 'auto'],
        maxWidth: 400,
        content: '<div style="padding: 20px; text-align: center;">' +
                '<div style="margin-bottom: 15px; color: #e67e22; font-size: 16px; font-weight: bold;">' +
                '<i class="iconfont icon-tixing" style="margin-right: 5px;"></i>重要提醒' +
                '</div>' +
                '<div style="margin-bottom: 15px; color: #333; line-height: 1.6;">' +
                '平台服务费抽成比例已调整为 <span style="color: #e67e22; font-weight: bold;">' + rateText + '</span>，您的招就办价格配置已自动重新计算，请及时查看。' +
                '</div>' +
                '<div style="margin-bottom: 20px; color: #7f8c8d; font-size: 12px;">' +
                '通知时间：' + dateStr +
                '</div>' +
                '</div>',
        btn: ['我已知晓并不再提醒', '我知道了'],
        btnAlign: 'c',
        yes: function(index) {
            // 永久清除通知标记
            clearZsbPriceChangeNotifyPermanently(stationId);
            layer.close(index);
        },
        btn2: function(index) {
            // 临时清除通知标记
            clearZsbPriceChangeNotifyTemporarily(stationId);
            layer.close(index);
        },
        cancel: function(index) {
            // 点击X或遮罩层，临时清除
            clearZsbPriceChangeNotifyTemporarily(stationId);
            layer.close(index);
        }
    });
}

// 永久清除招就办价格变化通知
function clearZsbPriceChangeNotifyPermanently(stationId) {
    $.ajax({
        url: '/prime/sys/clear_price_change_notify',
        type: 'POST',
        data: {
            station_id: stationId,
            permanent: 1  // 永久清除标记
        },
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                // 价格变化通知已永久清除
            }
        },
        error: function() {
            // 永久清除价格变化通知失败
        }
    });
}

// 临时清除招就办价格变化通知
function clearZsbPriceChangeNotifyTemporarily(stationId) {
    $.ajax({
        url: '/prime/sys/clear_price_change_notify',
        type: 'POST',
        data: {
            station_id: stationId,
            permanent: 0  // 临时清除标记
        },
        dataType: 'json',
        success: function(response) {
            if (response.status == 1) {
                // 价格变化通知已临时清除
            }
        },
        error: function() {
            // 临时清除价格变化通知失败
        }
    });
}

// 检查localStorage标记并隐藏招就办红点
function checkAndHideZjbNotifyDot() {
    // 检查是否有临时清除标记
    var tempCleared = localStorage.getItem('zjb_price_notify_temp_cleared');
    var permanentCleared = localStorage.getItem('zjb_price_notify_permanent_cleared');

    if (tempCleared === '1' || permanentCleared === '1') {

        // 隐藏招就办按钮的红点
        $('.nav a[href*="zjb"] .ico i').hide();

        // 也隐藏通过图片src定位的红点
        $('.nav .ico img[src*="tgy.png"]').siblings('i').hide();

        // 隐藏可能存在的其他红点标识
        if ($('#zjbNavNotifyDot').length > 0) {
            $('#zjbNavNotifyDot').hide();
        }

        // 如果是临时清除，检查时间是否过期（24小时）
        if (tempCleared === '1' && !permanentCleared) {
            var clearedTime = localStorage.getItem('zjb_price_notify_temp_cleared_time');
            if (clearedTime) {
                var timeDiff = Date.now() - parseInt(clearedTime);
                var hoursDiff = timeDiff / (1000 * 60 * 60);

                // 如果超过24小时，清除临时标记
                if (hoursDiff > 24) {
                    localStorage.removeItem('zjb_price_notify_temp_cleared');
                    localStorage.removeItem('zjb_price_notify_temp_cleared_time');
                }
            }
        }
    }
}

// 加载招就办余额数据
function loadZjbBalance() {
    $.ajax({
        url: '{:U("index/getZjbBalance")}',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.status === 1) {
                $('#total-balance').text(response.data.total_price);
                $('#available-balance').text(response.data.available_price);
                $('#freeze-balance').text(response.data.freeze_price);
            } else {
                // 获取余额失败
            }
        },
        error: function() {
            // 获取余额数据失败
        }
    });
}
</script>

<!-- 微信分享菜单隐藏功能 - 自动应用 -->
<if condition="isset($wechatHideShareScript)">
{$wechatHideShareScript}
</if>

</body>
</html>

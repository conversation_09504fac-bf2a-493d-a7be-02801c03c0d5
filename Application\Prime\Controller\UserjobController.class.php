<?php
namespace Prime\Controller;

use Common\Controller\PrimeController;

/**
 * 项目管理
 * Class ProjectController
 * @package Prime\Controller
 */
class UserjobController extends PrimeController
{

    public function _initialize()
    {
        parent::_initialize();
    }

    public function index() {
        $c_kw = [
            'name' => '姓名',
            'id' => 'ID',
            'service_station_id' => '服务站ID',
            'phone' => '电话',
            'id_number' => '身份证',
            'applied_position' => '应聘职位',
        ];
        $where = [];
        $s_kw = I("get.kw");
        $s_val = I("get.val");
        $s_status = I("get.status");
        $s_time = I("get.time");
        $s_start = strtotime($s_time['start']);
        $s_end = strtotime($s_time['end']);

        // 简历状态筛选
        if ($s_status !== '') $where['job_state'] = $s_status;

        // 基础搜索条件
        if ($s_kw && $s_val != '' && in_array($s_kw, array_keys($c_kw))) {
            if (in_array($s_kw, ['name', 'applied_position']))  {
                $where[$s_kw] = ['like', "%$s_val%"];
            }  else {
                $where[$s_kw] = $s_val;
            }
        }

        // 扩展搜索条件
        $gender = I("get.gender");
        $education_level = I("get.education_level");
        $service_station_id = I("get.service_station_id");
        $is_afraid_heights = I("get.is_afraid_heights");
        $has_training = I("get.has_training");

        if ($gender) $where['gender'] = $gender;
        if ($education_level) $where['education_level'] = $education_level;
        if ($service_station_id) $where['service_station_id'] = $service_station_id;
        if ($is_afraid_heights) $where['is_afraid_heights'] = $is_afraid_heights;

        // 培训报名搜索条件
        if ($has_training !== '') {
            if ($has_training == '1') {
                // 查找有培训订单的简历
                $trainingUserJobIds = D("TrainingOrder")->getField('user_job_id', true);
                if ($trainingUserJobIds) {
                    $where['id'] = ['in', $trainingUserJobIds];
                } else {
                    // 如果没有任何培训订单，设置一个不可能的条件
                    $where['id'] = ['eq', -1];
                }
            } elseif ($has_training == '0') {
                // 查找没有培训订单的简历
                $trainingUserJobIds = D("TrainingOrder")->getField('user_job_id', true);
                if ($trainingUserJobIds) {
                    $where['id'] = ['not in', $trainingUserJobIds];
                }
                // 如果没有任何培训订单，则不需要添加额外条件（所有简历都没有培训订单）
            }
        }

        //公用搜索
        $contions = I('get.');
        $contionsWhere = ['group', 'is_out'];
        foreach ($contions as $whereKey => $row) {
            if (in_array($whereKey, $contionsWhere) && $row != '') {
                $where[$whereKey] = $row;
            }
        }

        if ($s_start && $s_end) {
            $span = $s_end - $s_start;
            if ($span <= 0) {
                $this->error("请正确设置时间段");
                exit;
            }
            $where['create_time'] = ['between', [$s_start, $s_end]];
        }

        $obj = D("UserJob");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 20);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();

        // 获取所有服务站列表用于筛选下拉框和显示
        $allServiceStations = D("ServiceStation")->field('id,service_name,zsb_type')->select();
        $allServiceStationList = [];
        $serviceStationListForSelect = [];

        foreach ($allServiceStations as $station) {
            // 用于详情显示的完整数据结构
            $allServiceStationList[$station['id']] = [
                'service_name' => $station['service_name'],
                'zsb_type' => $station['zsb_type']
            ];
            // 用于下拉选择的简单结构
            $serviceStationListForSelect[$station['id']] = $station['service_name'];
        }

        $this->assign('serviceStationList', $allServiceStationList);
        $this->assign('serviceStationListForSelect', $serviceStationListForSelect);


        // 获取包含微信配置的服务站列表
        $serviceStationListWithWechat = [];
        $allStations = D("ServiceStation")->field('id,service_name,contract_name,chatroom,wxatuserlist,zsb_type,zsb_ref_station')->select();

        foreach ($allStations as $station) {
            $fwzservice_name = $serviceStationRow['service_name'];  
            $chatroom = $station['chatroom'];
            $wxatuserlist = $station['wxatuserlist'];

            // 如果当前服务站类型为2，则从关联的服务站获取微信配置
            if ($station['zsb_type'] == 2 && !empty($station['zsb_ref_station'])) {
                $refStation = null;
                // 在已获取的服务站列表中查找关联服务站
                foreach ($allStations as $refStationCandidate) {
                    if ($refStationCandidate['id'] == $station['zsb_ref_station']) {
                        $refStation = $refStationCandidate;
                        break;
                    }
                }

                // 如果在当前列表中没找到，再单独查询
                if (!$refStation) {
                    $refStation = D("ServiceStation")->where(['id' => $station['zsb_ref_station']])->find();
                }

                if ($refStation) {
                    $fwzservice_name =  $refStationRow['service_name'];
                    $chatroom = $refStation['chatroom'];
                    $wxatuserlist = $refStation['wxatuserlist'];
                }
            }

            $serviceStationListWithWechat[$station['id']] = [
                'service_name' => $fwzservice_name,
                'chatroom' => $chatroom,
                'wxatuserlist' => $wxatuserlist
            ];
        }
        $this->assign('serviceStationListWithWechat', $serviceStationListWithWechat);

        if ($list) {
            $userList = [];
            $userArrId = array_unique(array_column($list, 'user_id'));
            if ($userArrId) {
                $userList = D("User")->where(['id' => ['in', $userArrId]])->getField('id,nickname', true);
            }

            $userJobDocList = [];
            $userJobArrId = array_unique(array_column($list, 'id'));
            if ($userJobArrId) {
                $userJobDocList = D("UserJobDoc")->where(['user_job_id' => ['in', $userJobArrId]])->getField('user_job_id,content,is_html,status', true);
            }
            $this->assign('userJobDocList', $userJobDocList);

            // 获取最近的沟通记录和统计信息
            $recentMessagesList = [];
            $messageCountList = [];
            if ($userJobArrId) {
                $recentMessages = D("ServiceStationPlatformMessage")
                    ->where(['user_job_id' => ['in', $userJobArrId]])
                    ->order('create_time DESC')
                    ->select();

                // 按 user_job_id 分组，每个简历保留最近的5条消息，并统计消息数量
                foreach ($recentMessages as $message) {
                    $userJobId = $message['user_job_id'];

                    // 保存最近的5条消息
                    if (!isset($recentMessagesList[$userJobId])) {
                        $recentMessagesList[$userJobId] = [];
                    }
                    if (count($recentMessagesList[$userJobId]) < 5) {
                        $recentMessagesList[$userJobId][] = $message;
                    }

                    // 统计消息数量
                    if (!isset($messageCountList[$userJobId])) {
                        $messageCountList[$userJobId] = 0;
                    }
                    $messageCountList[$userJobId]++;
                }
            }

            // 获取消息中涉及的所有服务站信息，用于判断身份
            $allMessages = [];
            foreach ($recentMessagesList as $messages) {
                $allMessages = array_merge($allMessages, $messages);
            }
            $messageServiceStationIds = array_unique(array_column($allMessages, 'service_station_id'));
            $messageServiceStationMap = [];
            if ($messageServiceStationIds) {
                $messageServiceStations = D("ServiceStation")->where(['id' => ['in', $messageServiceStationIds]])->select();
                foreach ($messageServiceStations as $station) {
                    $messageServiceStationMap[$station['id']] = $station;
                }
            }

            $this->assign('recentMessagesList', $recentMessagesList);
            $this->assign('messageCountList', $messageCountList);
            $this->assign('messageServiceStationMap', $messageServiceStationMap);

            // 获取培训订单信息
            $trainingOrderList = [];
            if ($userJobArrId) {
                $trainingOrders = D("TrainingOrder")
                    ->field('id,user_job_id')
                    ->where(['user_job_id' => ['in', $userJobArrId]])
                    ->select();

                foreach ($trainingOrders as $order) {
                    $trainingOrderList[$order['user_job_id']] = $order['id'];
                }
            }
            $this->assign('trainingOrderList', $trainingOrderList);

            //用户微信昵称
            $this->assign('userList', $userList);
        }

        // 简历状态定义
        $statusList = [
            '0' => ['text' => '沟通中', 'style' => 'info'],
            '1' => ['text' => '培训中', 'style' => 'warning'],
            '2' => ['text' => '已入职', 'style' => 'success'],
            '3' => ['text' => '服务终止', 'style' => 'danger'],
        ];

        // 计算统计数据（不受搜索条件影响的全局统计）
        $statsObj = D("UserJob");

        // 总简历数
        $totalResumes = $statsObj->count();

        // 沟通中的简历数（job_state = 0）
        $activeResumes = $statsObj->where(['job_state' => 0])->count();

        // 待回复的简历数（有未回复的消息）
        $needReplyCount = 0;

        // 获取所有有消息的简历ID
        $messageUserJobIds = D("ServiceStationPlatformMessage")
            ->distinct(true)
            ->field('user_job_id')
            ->select();

        if ($messageUserJobIds) {
            $messageUserJobIdList = array_column($messageUserJobIds, 'user_job_id');

            // 对每个有消息的简历，检查最新消息是否需要回复
            foreach ($messageUserJobIdList as $userJobId) {
                $latestMessage = D("ServiceStationPlatformMessage")
                    ->where(['user_job_id' => $userJobId])
                    ->order('create_time DESC')
                    ->find();

                if ($latestMessage && $latestMessage['need_reply'] == 1) {
                    $needReplyCount++;
                }
            }
        }

        $statsData = [
            'total_resumes' => $totalResumes,
            'active_resumes' => $activeResumes,
            'need_reply' => $needReplyCount
        ];

        $this->assign('statsData', $statsData);

        $this->assign('s_start', $s_start ? date('Y-m-d H:i', $s_start) : '');
        $this->assign('s_end', $s_end ? date('Y-m-d H:i', $s_end) : '');
        $this->assign('_get', I('get.'));
        $this->assign('list',$list);
        $this->assign("page", $page->show());
        $this->assign('statusList', $statusList);
        $this->assign('c_kw', $c_kw);
        $this->display();
    }

    /**
     * 快速回复消息
     */
    public function quickReply() {
        // 记录请求日志
        dolog('quickreply/request', 'QuickReply request received');

        // 设置响应头为JSON格式
        header('Content-Type: application/json; charset=utf-8');

        if (!IS_POST) {
            dolog('quickreply/error', 'Invalid request method: ' . $_SERVER['REQUEST_METHOD']);
            echo json_encode(['status' => 0, 'info' => '请求方式错误']);
            exit;
        }

        $user_job_id = intval(I('post.user_job_id'));
        $content = trim(I('post.content'));
        $need_reply = I('post.need_reply');
        $send_wechat = I('post.send_wechat');

        // 记录参数日志
        dolog('quickreply/params', 'user_job_id=' . $user_job_id . ', content_length=' . strlen($content));

        if (!$user_job_id) {
            echo json_encode(['status' => 0, 'info' => '简历ID不能为空']);
            exit;
        }

        if (empty($content)) {
            echo json_encode(['status' => 0, 'info' => '回复内容不能为空']);
            exit;
        }

        // 获取简历信息
        $jobRow = D("UserJob")->where(['id' => $user_job_id])->find();
        if (!$jobRow) {
            echo json_encode(['status' => 0, 'info' => '简历不存在']);
            exit;
        }

        // 获取服务站信息
        $serviceStationRow = D("ServiceStation")->where(['id' => $jobRow['service_station_id']])->find();
        if (!$serviceStationRow) {
            echo json_encode(['status' => 0, 'info' => '服务站不存在']);
            exit;
        }

        // 使用与MessageController相同的方式处理数据
        $obj = D("ServiceStationPlatformMessage");

        if ($data = $obj->create()) {
            // 设置消息数据
            $data['create_time'] = time();
            $data['type'] = 2; // 平台回复
            $data['service_station_id'] = $jobRow['service_station_id'];
            $data['user_job_id'] = $user_job_id;

            // 添加消息记录
            $messageId = $obj->add($data);

            // 记录插入结果
            dolog('quickreply/insert_result', 'Message inserted with ID: ' . $messageId);

            if ($messageId) {

                // 更新简历状态
                $updateData = [
                    'is_reply' => 1,
                    'msg_reply_time' => time()
                ];

                // 处理提醒回复功能
                if (I('post.need_reply') == '1') {
                    $updateData['need_reply'] = 1;

                    // 发送短信提醒给服务站
                    if ($serviceStationRow && !empty($serviceStationRow['mobile']) && !empty($jobRow['name'])) {
                        try {
                            $aliSms = new \Util\AliSms();
                            // 使用现有的短信模板发送提醒，与MessageController保持一致
                            $smsText = "您好，简历用户【{$jobRow['name']}】有新的平台消息需要回复，请及时查看处理。";
                            $smsResult = $aliSms->sendSms($smsText, $serviceStationRow['mobile'], 'SMS_479770174', '{"code":"' . $smsText . '"}');

                            if ($smsResult) {
                                dolog('sms/quickreply_reminder_success', "服务站: {$serviceStationRow['service_name']}, 简历用户: {$jobRow['name']}, 时间: " . date('Y-m-d H:i:s'));
                            } else {
                                dolog('sms/quickreply_reminder_failed', "服务站: {$serviceStationRow['service_name']}, 简历用户: {$jobRow['name']}, 时间: " . date('Y-m-d H:i:s'));
                            }
                        } catch (Exception $e) {
                            dolog('sms/quickreply_reminder_exception', "服务站: {$serviceStationRow['service_name']}, 简历用户: {$jobRow['name']}, 错误: " . $e->getMessage());
                        }
                    }
                } else {
                    $updateData['need_reply'] = 0;
                }

                // 更新简历状态
                $updateResult = D("UserJob")->where(['id' => $user_job_id])->save($updateData);

                if ($updateResult !== false) {
                    // 处理微信通知功能
                    if (I('post.send_wechat') == '1') {
                        // 检查服务站的chatroom和wxatuserlist字段
                        // 根据服务站类型获取微信群聊配置
                        $fwzservice_name = $serviceStationRow['service_name'];  
                        $chatroom = $serviceStationRow['chatroom'];
                        $wxatuserlist = $serviceStationRow['wxatuserlist'];
                        $zjbname = '';
                        $tzzjb = '';
                        if ($serviceStationRow['zsb_type'] == 2) {
                            $zjbname = '【招就办-'.$serviceStationRow['contract_name'].'】';
                            $tzzjb = '请及时告知【招就办-'.$serviceStationRow['contract_name'].'】';
                        }

                        // 如果当前服务站类型为2，则从关联的服务站获取微信配置
                        if ($serviceStationRow['zsb_type'] == 2 && !empty($serviceStationRow['zsb_ref_station'])) {
                            $refStationRow = D("ServiceStation")->where(['id' => $serviceStationRow['zsb_ref_station']])->find();
                            if ($refStationRow) {
                                $fwzservice_name =  $refStationRow['service_name'];
                                $chatroom = $refStationRow['chatroom'];
                                $wxatuserlist = $refStationRow['wxatuserlist'];
                            }
                        }

                        if ($serviceStationRow && !empty($chatroom) && !empty($wxatuserlist)) {
                            // 构建微信通知内容
                            $smscontent = $fwzservice_name . "，平台回复了".$zjbname."【{$jobRow['name']}】简历，请及时".$tzzjb."查看处理。\n\n回复内容如下：\n\n{$data['content']}";

                            try {
                                $this->sendWechatNotification($smscontent, $chatroom, $wxatuserlist);
                                dolog('quickreply/wechat_success', 'WeChat notification sent for user_job_id=' . $user_job_id);
                            } catch (Exception $e) {
                                dolog('quickreply/wechat_error', 'WeChat notification failed for user_job_id=' . $user_job_id . ', error: ' . $e->getMessage());
                            }
                        } else {
                            dolog('quickreply/wechat_skip', 'WeChat notification skipped for user_job_id=' . $user_job_id . ' - missing chatroom or wxatuserlist data in service station');
                        }
                    }

                    // 返回成功响应
                    dolog('quickreply/success', 'QuickReply success: message_id=' . $messageId . ', user_job_id=' . $user_job_id);
                    echo json_encode([
                        'status' => 1,
                        'info' => '快速回复发送成功',
                        'data' => [
                            'message_id' => $messageId,
                            'user_job_id' => $user_job_id
                        ]
                    ]);
                } else {
                    dolog('quickreply/error', 'Failed to update UserJob status for user_job_id=' . $user_job_id);
                    echo json_encode(['status' => 0, 'info' => '更新简历状态失败，请重试']);
                }
            } else {
                // 消息添加失败
                dolog('quickreply/error', 'Failed to add message record for user_job_id=' . $user_job_id);
                echo json_encode(['status' => 0, 'info' => '回复失败，请重试']);
            }
        } else {
            // 数据验证失败
            $error = $obj->getError();
            dolog('quickreply/create_error', 'Data validation failed: ' . $error);
            echo json_encode(['status' => 0, 'info' => $error ?: '数据验证失败，请检查输入内容']);
        }
        exit;
    }

    /**
     * 测试快速回复功能
     */
    public function testQuickReply() {
        header('Content-Type: application/json; charset=utf-8');

        // 测试数据库连接
        try {
            $messageObj = D("ServiceStationPlatformMessage");
            $testQuery = $messageObj->where(['id' => 1])->find();

            $userJobObj = D("UserJob");
            $testUserJob = $userJobObj->where(['id' => 1])->find();

            echo json_encode([
                'status' => 1,
                'info' => '快速回复功能测试通过',
                'data' => [
                    'message_table_accessible' => $testQuery !== false,
                    'userjob_table_accessible' => $testUserJob !== false,
                    'current_time' => time(),
                    'date' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'status' => 0,
                'info' => '测试失败：' . $e->getMessage()
            ]);
        }
        exit;
    }

   /**
     * 更多简历信息
     */
    public function joballinfo() {
        $id = I('get.id', 0, 'intval'); // 强制转为整数
        if ($id <= 0) {
            $this->error('参数错误');
        }
    
        // 主表数据
        $userJob = D('UserJob')->find($id);
        if (!$userJob) {
            $this->error('简历不存在');
        }
    
        // 关联表查询条件
        $condition = ['user_job_id' => $id];
    
        // 教育经历
        $educationList = D('Education')
            ->where($condition)
            ->order('id asc')  
            ->select();
    
        // 家庭成员
        $familyMembers = D('FamilyMembers')
            ->where($condition)
            ->order('id asc')
            ->select();
    
        // 工作经历
        $workExperience = D('WorkExperience')
            ->where($condition)
            ->order('id asc')
            ->select();
    
        // 技能证书
        $skillsCertificates = D('SkillsCertificates')
            ->where($condition)
            ->order('id asc')
            ->select();
    
        // 数据分配
        $this->assign([
            'userJob' => $userJob, // 主表数据
            'educationList' => $educationList,
            'familyMembers' => $familyMembers,
            'workExperience' => $workExperience,
            'skillsCertificates' => $skillsCertificates
        ]);
    
        $this->display(); // 自动匹配模板文件
    }



    public function h5() {
        $id = I('get.id', 0);
        if (!$id) $this->error('参数错误');
        $userJobDocRow = D("UserJobDoc")->where(['user_job_id' => $id])->find();
        if (!$userJobDocRow) $this->error('当前简历不存在');
        if (empty($userJobDocRow['html_content'])) $this->error('当前简历未分析成功，请稍后再来!!');
        $this->assign('userJobDocRow', $userJobDocRow);
        $this->display();
    }

    public function education() {
        $userJobId = I('get.job_id', 0);
        if (!$userJobId) $this->error('参数错误!!', U("userjob/index"));
        $where = [
            'user_job_id' => $userJobId,
        ];
        $obj = D("Education");
        $sort_param = sortParam('id', 'asc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->where($where)
            ->select();
        $userJobRow = D("UserJob")->where(['id' => $userJobId])->find();
        $this->assign('userJobRow', $userJobRow);
        $this->assign('list',$list);
        $this->display();
    }

    /**
     * 添加编辑
     */
    public function educationedit() {
        $id = intval(I('get.id'));
        $obj = D("Education");
        if ($id) {
            $row = $obj->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);
        } else {
            $this->error('参数错误');
        }
        $userJobRow = D("UserJob")->where(['id' => $row['user_job_id']])->find();
        $this->assign('userJobRow', $userJobRow);;


        if (IS_POST) {
            if ($data = $obj->create()) {
                if (!$id) {
                    $data['create_time'] = time();
                    $obj->add($data);
                } else {
                    $data['id'] = $id;
                    $obj->save($data);
                }
                $this->success("操作成功", U("userjob/education", ['job_id' => $row['user_job_id']]));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $this->display();
    }



    public function familymembers() {
        $userJobId = I('get.job_id', 0);
        if (!$userJobId) $this->error('参数错误!!', U("userjob/index"));
        $where = [
            'user_job_id' => $userJobId,
        ];
        $obj = D("FamilyMembers");
        $sort_param = sortParam('id', 'asc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->where($where)
            ->select();
        $userJobRow = D("UserJob")->where(['id' => $userJobId])->find();
        $this->assign('userJobRow', $userJobRow);
        $this->assign('list',$list);
        $this->display();
    }

    /**
     * 添加编辑
     */
    public function familymembersedit() {
        $id = intval(I('get.id'));
        $obj = D("FamilyMembers");
        if ($id) {
            $row = $obj->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);
        } else {
            $this->error('参数错误');
        }
        $userJobRow = D("UserJob")->where(['id' => $row['user_job_id']])->find();
        $this->assign('userJobRow', $userJobRow);;


        if (IS_POST) {
            if ($data = $obj->create()) {
                if (!$id) {
                    $data['create_time'] = time();
                    $obj->add($data);
                } else {
                    $data['id'] = $id;
                    $obj->save($data);
                }
                $this->success("操作成功", U("userjob/familymembers", ['job_id' => $row['user_job_id']]));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $this->display();
    }

    /**
     * 工作经历
     */
    public function workexperience() {
        $userJobId = I('get.job_id', 0);
        if (!$userJobId) $this->error('参数错误!!', U("userjob/index"));
        $where = [
            'user_job_id' => $userJobId,
        ];
        $obj = D("WorkExperience");
        $sort_param = sortParam('id', 'asc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->where($where)
            ->select();
        $userJobRow = D("UserJob")->where(['id' => $userJobId])->find();
        $this->assign('userJobRow', $userJobRow);
        $this->assign('list',$list);
        $this->display();
    }

    /**
     * 添加编辑
     */
    public function workexperienceedit() {
        $id = intval(I('get.id'));
        $obj = D("WorkExperience");
        if ($id) {
            $row = $obj->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);
        } else {
            $this->error('参数错误');
        }
        $userJobRow = D("UserJob")->where(['id' => $row['user_job_id']])->find();
        $this->assign('userJobRow', $userJobRow);;


        if (IS_POST) {
            if ($data = $obj->create()) {
                if (!$id) {
                    $data['create_time'] = time();
                    $obj->add($data);
                } else {
                    $data['id'] = $id;
                    $obj->save($data);
                }
                $this->success("操作成功", U("userjob/workexperience", ['job_id' => $row['user_job_id']]));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $this->display();
    }


    /**
     * 工作经历
     */
    public function skillscertificates() {
        $userJobId = I('get.job_id', 0);
        if (!$userJobId) $this->error('参数错误!!', U("userjob/index"));
        $where = [
            'user_job_id' => $userJobId,
        ];
        $obj = D("SkillsCertificates");
        $sort_param = sortParam('id', 'asc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->where($where)
            ->select();
        $userJobRow = D("UserJob")->where(['id' => $userJobId])->find();
        $this->assign('userJobRow', $userJobRow);
        $this->assign('list',$list);
        $this->display();
    }

    /**
     * 技能证书
     */
    public function skillscertifedit() {
        $id = intval(I('get.id'));
        $obj = D("SkillsCertificates");
        if ($id) {
            $row = $obj->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);
        } else {
            $this->error('参数错误');
        }
        $userJobRow = D("UserJob")->where(['id' => $row['user_job_id']])->find();
        $this->assign('userJobRow', $userJobRow);;


        if (IS_POST) {
            if ($data = $obj->create()) {
                if (!$id) {
                    $data['create_time'] = time();
                    $obj->add($data);
                } else {
                    $data['id'] = $id;
                    $obj->save($data);
                }
                $this->success("操作成功", U("userjob/skillscertificates", ['job_id' => $row['user_job_id']]));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $this->display();
    }


/**
 * 删除简历（安全增强版）
 */
public function deljob() {
    $id = I('get.id/d', 0); // 强制转为整数 + 默认值0
    if ($id <= 0) {
        $this->error('参数错误'); // 直接拦截非法ID
    }

    $obj = D("UserJob");
    // 前置验证（带共享锁，防止并发修改）
    $row = $obj->where(['id' => $id])->lock(true)->find();
    if (!$row) {
        $this->error('记录不存在或已被删除');
    }

    // 启用事务（确保在数据有效后开启）
    $obj->startTrans();
    try {
        // 1. 删除关联的UserJobDoc记录
        $userJobDocObj = D("UserJobDoc");
        $docDeleteResult = $userJobDocObj->where(['user_job_id' => $id])->delete();

        // 记录删除的文档数量（用于日志）
        $deletedDocCount = $docDeleteResult;

        // 2. 删除主表UserJob记录
        $deleteResult = $obj->where(['id' => $id])->delete();

        // 严格判断删除结果（受影响行数应=1）
        if ($deleteResult !== 1) {
            throw new \Exception('删除操作未生效');
        }

        $obj->commit();

        // 记录删除日志
        dolog('userjob/delete', "删除简历成功: user_job_id={$id}, 同时删除关联文档{$deletedDocCount}条");

        $this->success('删除成功');
    } catch (\Exception $e) {
        $obj->rollback();

        // 记录错误日志
        dolog('userjob/delete_error', "删除简历失败: user_job_id={$id}, error=" . $e->getMessage());

        $this->error('删除失败：'.$e->getMessage());
    }
}


    /**
     * 添加编辑
     */
    public function edit() {
        $id = intval(I('get.id'));
        $obj = D("UserJob");
        if ($id) {
            $row = $obj->where("id=".$id)->find();
            if (!$row) $this->error('参数错误');
            $this->assign('row',$row);

            // 检查该简历是否存在培训订单
            $hasTrainingOrder = D("TrainingOrder")->where(['user_job_id' => $id])->count() > 0;
            $this->assign('hasTrainingOrder', $hasTrainingOrder);
        }

        if (IS_POST) {
            if ($data = $obj->create()) {
                if (!$id) {
                    $data['create_time'] = time();
                    $obj->add($data);
                } else {
                    $data['id'] = $id;
                    $obj->save($data);
                }
                $this->success("操作成功", U("userjob/index"));
                exit;
            } else {
                $this->error($obj->getError());
                exit;
            }
        }
        $serviceStationList = D("ServiceStation")->getField('id,service_name', true);
        $this->assign('serviceStationList', $serviceStationList);
        $this->display();
    }

    /**
     * 上传简历功能
     * @return voids
     */
    public function upjob() {
        $id = I('get.id', 0);

        if ($id) {
            $row = D("UserJob")->where(['id' => $id])->find();
            if ($row) {
                $row['files'] = D("UserJobDoc")->where(['user_job_id' => $row['id']])->getField('content');
            }
        }
        $serviceStationList = D("ServiceStation")->getField('id,service_name', true);
        $this->assign('serviceStationList', $serviceStationList);
        if (IS_POST) {
            $service_station_id = I('post.service_station_id');
            $files = I('post.files');
            if (!$service_station_id) $this->error('请选择正确的服务站');
            if (!$files) $this->error('请上传正确的简历');
            $files = '/'.$files;
            if (!$row) {
                $serviceStationUserRow = D("User")->where(['service_id' => 1, 'self_service_station_id' => $service_station_id, 'is_first_wechat' => 1])->find();
                if (!$serviceStationUserRow) $this->error('当前服务站请先绑定微信，先去网页版本登录!');
                $fileHash = md5_file(SITE_PATH . $files);
                $userJobId = D("UserJob")->add([
                    'user_id' => $serviceStationUserRow['id'],
                    'remark' => '',
                    'service_station_id' => $service_station_id,
                    'type' => 2,
                    'create_time' => time(),
                ]);
                if ($userJobId) {

                    D("UserJobDoc")->add([
                        'user_id' => $serviceStationUserRow['id'],
                        'user_job_id' => $userJobId,
                        'file_hash' => $fileHash,
                        'file_names' => '',
                        'service_station_id' => $service_station_id,
                        'content' => $files,
                        'status' => 1,
                        'create_time' => time(),
                    ]);
                    return $this->success('添加简历成功', U('userjob/index'));
                }
            } else {
                $fileHash = md5_file(SITE_PATH . $files);
                $userJobDocRow = D("UserJobDoc")->where(['user_job_id' => $row['id']])->find();
                if ($userJobDocRow) {
                    if ($fileHash == $row['file_hash']) {
                        $this->error('未更新简历文件');
                    }
                    D("UserJobDoc")->save([
                        'id' => $userJobDocRow['id'],
                        'file_hash' => $fileHash,
                        'file_names' => '',
                        'service_station_id' => $service_station_id,
                        'content' => $files,
                        'is_html' => 1,
                        'status' => 1,
                        'create_time' => time(),
                    ]);
                    return $this->success('更新简历文件成功', U('userjob/index'));
                }
            }
            $this->error('更新简历失败');
        }
        $this->assign('row', $row);
        $this->display();
    }

    /**
     * 状态变更
     */
    public function cgstat() {
        $id = I('get.id');
        $status = I('get.status');
        if (!$id) $this->error('参数错误');
        $obj = D("Project");
        if (!array_key_exists($status, $obj->status)) $this->error('参数错误 ');
        $obj->save(['id' => $id, 'status' => $status]);
        if ($status == 0) {
            D("ProjectPost")->where(['project_id' => $id, 'status' => 1])->save(['status' => 0]);
        }
        $this->success('修改成功');
    }

    public function upstatus() {
        $id = I('get.job_id');
        if (!$id) $this->error('参数错误');
        $obj = D("UserJobDoc");
        $obj->where(['user_job_id' => $id])->save(['status' => 1]);
        $this->success('修改成功');
    }

    public function uph5() {
        $id = I('get.job_id');
        if (!$id) $this->error('参数错误');
        $obj = D("UserJobDoc");
        $obj->where(['user_job_id' => $id])->save(['is_html' => 1]);
        $this->success('修改成功');
    }

    public function quotation() {

        $c_kw = [
            'name' => '项目名称',
            'id' => 'ID',
        ];
        $where = [];
        $obj = D("ProjectPost");
        $count = $obj->where($where)->count();
        $page = $this->page($count, 100);
        $sort_param = sortParam('id', 'desc');
        $list = $obj
            ->order("{$sort_param['field']} {$sort_param['order']}")
            ->limit($page->firstRow . ',' . $page->listRows)
            ->where($where)
            ->select();
        if ($list) {
            $projectArrId = array_unique(array_column($list, 'project_id'));
            $projectList = [];
            if ($projectArrId) {
                $projectList = D("Project")->where(['id' => ['in', $projectArrId]])->getField('id,name', true);
            }
            $this->assign('projectList', $projectList);
            $projectPostArrId = array_unique(array_column($list, 'id'));
            $projectJoinIdentity = [];
            if ($projectPostArrId) {
                $projectJoinIdentityList = D("ProjectJoinIdentity")->where(['project_post_id' => ['in', $projectPostArrId]])->select();
                $this->assign('projectJoinIdentity', $projectJoinIdentity);
                foreach ($projectJoinIdentityList as $projectJoinIdentityRow) {
                    $projectJoinIdentity[$projectJoinIdentityRow['project_post_id']][$projectJoinIdentityRow['project_identity_id']] = $projectJoinIdentityRow['cost'];
                }
            }
            $this->assign('projectJoinIdentity', $projectJoinIdentity);
        }
        $projectIdentityList = D("ProjectIdentity")->where(['status' => 1])->Field('id,name')->select();
        $this->assign('projectIdentityList', $projectIdentityList);
        $this->assign('_get', I('get.'));
        $this->assign('list',$list);
        $this->assign("page", $page->show());
        $this->assign('c_kw', $c_kw);
        $this->display();
    }

    /**
     * 设置报价
     */
    public function setquotation() {
        $projectPostId = I('get.project_post_id', 0);
        if (!$projectPostId) $this->error('参数错误数!!!');
        $obj = D("ProjectPost");
        $projectPostRow = $obj->where(['id' => $projectPostId])->find();
        $this->assign('row', $projectPostRow);

        $projectList = [];
        if ($projectPostRow) {
            $projectList = D("Project")->where(['id' => $projectPostRow['project_id']])->getField('id,name', true);
        }
        $this->assign('projectList', $projectList);
        $projectIdentityList = D("ProjectIdentity")->where(['status' => 1])->Field('id,name')->select();
        $this->assign('projectIdentityList', $projectIdentityList);
        if (IS_POST) {
            $data = I('post.');
            foreach ($data['projectidentity'] as $projectidentityId => $val) {
                $projectJoinIdentityRow = [];
                $projectJoinIdentityRow = D("ProjectJoinIdentity")->where([
                    'project_post_id' => $projectPostId,
                    'project_identity_id' => $projectidentityId,
                ])->find();
                if (!$projectJoinIdentityRow) {
                    $insertId = D("ProjectJoinIdentity")->add([
                        'project_id' => $projectPostRow['project_id'],
                        'project_post_id' => $projectPostId,
                        'project_identity_id' => $projectidentityId,
                        'cost' => $val,
                        'create_time' => time(),
                    ]);
                } else {
                    D("ProjectJoinIdentity")->save([
                        'id' => $projectJoinIdentityRow['id'],
                        'cost' => $val,
                    ]);
                }

            }

            return $this->success('更新报价成功', U("project/quotation"));
        }
        $projectJoinIdentity = [];
        $projectJoinIdentityList = D("ProjectJoinIdentity")->where(['project_post_id' => $projectPostId])->select();
        foreach ($projectJoinIdentityList as $projectJoinIdentityRow) {
            $projectJoinIdentity[$projectJoinIdentityRow['project_post_id']][$projectJoinIdentityRow['project_identity_id']] = $projectJoinIdentityRow['cost'];
        }
        $this->assign('projectJoinIdentity', $projectJoinIdentity);
        $this->display();
    }

    /**
     * 发送微信消息通知
     * @param string $content 回复内容
     * @param string $chatroom 群聊ID
     * @param string $wxatuserlist @用户列表
     */
    private function sendWechatNotification($content, $chatroom, $wxatuserlist) {
        try {
            // 构建POST数据
            $postData = [
                'WxID' => 'zcgk666',
                'Data' => [
                    'SendTo' => $chatroom,
                    'Msg' => '@' . $content,
                    'AtUserList' => $wxatuserlist,
                    'ArgList' => ['SendTo', 'Msg', 'AtUserList'],
                    'AnyCallName' => 'SendMessageAt',
                    'TimeOut' => -1,
                    'CallName' => 'Any'
                ],
                'CallBackUrl' => null
            ];

            // 转换为JSON格式
            $jsonData = json_encode($postData, JSON_UNESCAPED_UNICODE);

            // 记录发送日志
            dolog('wechat_notification/send', 'Sending notification: ' . $jsonData);

            // 初始化cURL
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://43.139.30.237:1080/HTTPManagement.aspx?Item=AddMessage');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($jsonData)
            ]);

            // 执行请求
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            // 记录响应日志
            if ($error) {
                dolog('wechat_notification/error', 'cURL error: ' . $error);
            } else {
                dolog('wechat_notification/response', 'HTTP Code: ' . $httpCode . ', Response: ' . $response);
            }

            return $response;

        } catch (Exception $e) {
            // 记录异常日志
            dolog('wechat_notification/exception', 'Exception: ' . $e->getMessage());
            return false;
        }
    }
}
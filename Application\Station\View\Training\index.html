<!DOCTYPE html>
<html>
  <head>
    <title>培训订单管理</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta
      content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"
      name="viewport"
    />
    <meta content="no-cache,must-revalidate" http-equiv="Cache-Control" />
    <meta content="telephone=no, address=no" name="format-detection" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta content="no-cache" http-equiv="pragma" />
    <meta content="0" http-equiv="expires" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="/static/stations/css/css.css?v={:time()}"
      media="all"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="/static/stations/css/swiper-bundle.css"
      media="all"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="/static/stations/css/iconfont.css"
      media="all"
    />
    <script
      type="text/javascript"
      src="/static/stations/js/jquery-1.9.1.min.js"
    ></script>
    <script
      type="text/javascript"
      src="/static/stations/js/swiper-bundle.min.js"
    ></script>
    <script src="/static/js/layer/layer.js"></script>
    <!-- 微信JSSDK -->
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script>
      // 全局错误处理
      window.onerror = function(message, source, lineno, colno, error) {
        console.error("JS错误: ", message, "在", source, "行:", lineno);
        return false;
      };

      // 确保layer对象可用
      window.showMessage = function(msg, type) {
        if (window.layer && typeof window.layer.msg === 'function') {
          layer.msg(msg, {icon: type || 2});
        } else {
          alert(msg);
        }
      };
    </script>
  </head>
  <style>
    .container {
      margin: 10px;
    }
    .nomore {
      display: block;
      align-items: center;
      text-align: center;
      color: #ccc;
    }

    /* 添加按钮样式 */
    .add-btn {
      margin-top: 2px;
      padding: 10px 18px;
      background: #ff6b35;
      color: white;
      border: none;
      border-radius: 10px;
      font-size: 17px;
      cursor: pointer;
      transition: all 0.2s;
      box-shadow: 0 4px 12px rgba(0, 191, 128, 0.3);
    }

    .add-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 191, 128, 0.4);
    }

    /* 搜索框样式 */
    .search-bar {
      background: #fff;
      padding: 8px 15px;
      position: sticky;
      top: 0;
      z-index: 100;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .search-input {
      width: 100%;
      height: 36px;
      border: 1px solid #e5e5e5;
      border-radius: 18px;
      padding: 0 15px;
      box-sizing: border-box;
      background: #f0f0f0;
      font-size: 14px;
      margin-right: 10px;
    }

    .search-select {
      height: 36px;
      border: 1px solid #e5e5e5;
      border-radius: 18px;
      padding: 0 10px;
      box-sizing: border-box;
      background: #f0f0f0;
      font-size: 14px;
      margin-right: 10px;
    }

    /* 订单列表项 */
    .order-item {
      background: #fff;
      border-radius: 12px;
      margin: 15px 0;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      border: 1px solid #f0f0f0;
      transition: all 0.2s ease;
    }

    .order-item:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
      transform: translateY(-1px);
    }

    /* 订单头部信息 - 两列布局 */
    .order-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;
      padding-bottom: 10px;
      border-bottom: 1px solid #f5f5f5;
    }

    .header-left {
      flex: 1;
    }

    .header-right {
      flex: 0 0 auto;
      text-align: right;
      margin-left: 15px;
      align-self: flex-start;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }

    .order-title-row {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      flex-wrap: wrap;
      gap: 8px;
    }

    .order-number {
      font-size: 15px;
      font-weight: 600;
      color: #333;
      margin-right: 0;
    }

    .order-time {
      font-size: 12px;
      color: #999;
      margin-bottom: 2px;
    }

    .zsb-contact {
      font-size: 12px;
      color: #007aff !important;
      font-weight: 500 !important;
      background: #f0f8ff !important;
      padding: 4px 8px !important;
      border-radius: 12px !important;
      border: 1px solid #e3f2fd !important;
      display: inline-block !important;
      margin-top: 6px !important;
      white-space: nowrap;
    }

    /* 主要信息区域 */
    .basic-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      gap: 16px;
    }

    .left-info {
      flex: 1;
      min-width: 0; /* 允许文本截断 */
    }

    .right-info {
      flex-shrink: 0;
      text-align: left;
      min-width: 120px;
    }

    .left-info > div,
    .right-info > div {
      margin: 6px 0;
      line-height: 1.4;
    }

    /* 学员信息样式 */
    .user-info {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }

    .project-info {
      font-size: 13px;
      color: #666;
    }

    .zsb-info {
      font-size: 12px;
      color: #888;
    }

    /* 价格信息样式 */
    .price-info {
      font-size: 13px;
      color: #333;
    }

    .price-amount {
      font-weight: 600;
      font-size: 14px;
    }

    .price-positive {
      color: #07c160;
    }

    .price-negative {
      color: #ff3b30;
    }

    /* 状态信息区域 */
    .position-info {
      margin: 12px 0 8px 0;
      padding: 10px 0;
      border-top: 1px solid #f0f0f0;
    }

    .status-row {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 8px;
      margin-bottom: 4px;
    }

    .status-time {
      font-size: 11px;
      color: #999;
      white-space: nowrap;
      text-align: left;
    }

    /* 按钮区域 */
    .action-area {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 8px;
      padding-top: 8px;
    }

    .btn {
      padding: 6px 15px;
      border-radius: 15px;
      font-size: 13px;
      border: none;
      margin-left: 8px;
    }

    .btn-primary {
      background: #07c160;
      color: white;
    }

    /* 状态标签基础样式 */
    .status-tag {
      display: inline-flex;
      align-items: center;
      border-radius: 16px;
      font-weight: 500;
      margin-right: 6px;
      margin-bottom: 4px;
      white-space: nowrap;
    }

    /* 主状态标签 - 较大且突出 */
    .status-tag.main-status {
      padding: 6px 12px;
      font-size: 13px;
      font-weight: 600;
    }

    /* 子状态标签 - 较小且次要 */
    .status-tag.sub-status {
      padding: 4px 8px;
      font-size: 11px;
      font-weight: 400;
      border: 1px solid;
      background: transparent !important;
      margin-left: 4px;
    }

    .status-tag i {
      margin-right: 4px;
      font-size: 11px;
    }

    /* 主状态样式 */
    .status-tag.main-status.status-communication {
      background: #1976d2;
      color: white;
    }

    .status-tag.main-status.status-training {
      background: #f57c00;
      color: white;
    }

    .status-tag.main-status.status-employment {
      background: #388e3c;
      color: white;
    }

    .status-tag.main-status.status-service_review {
      background: #c2185b;
      color: white;
    }

    /* 层级状态颜色样式 */
    .status-tag.hierarchical-status.status-communication {
      background: #1976d2;
      color: white;
    }

    .status-tag.hierarchical-status.status-training {
      background: #f57c00;
      color: white;
    }

    .status-tag.hierarchical-status.status-employment {
      background: #388e3c;
      color: white;
    }

    .status-tag.hierarchical-status.status-service_review {
      background: #c2185b;
      color: white;
    }

    /* 子状态样式 - 继承主状态的颜色作为边框色 */
    .status-tag.sub-status.status-communication {
      border-color: #1976d2;
      color: #1976d2;
    }

    .status-tag.sub-status.status-training {
      border-color: #f57c00;
      color: #f57c00;
    }

    .status-tag.sub-status.status-employment {
      border-color: #388e3c;
      color: #388e3c;
    }

    .status-tag.sub-status.status-service_review {
      border-color: #c2185b;
      color: #c2185b;
    }

    /* 默认子状态样式 */
    .status-tag.sub-status {
      border-color: #999;
      color: #666;
    }

    /* 层级状态样式 - 主状态/子状态 */
    .status-tag.hierarchical-status {
      padding: 6px 12px;
      font-size: 13px;
      font-weight: 500;
      display: inline-flex;
      align-items: center;
    }

    .status-tag.hierarchical-status .main-text {
      font-weight: 600;
    }

    .status-tag.hierarchical-status .separator {
      margin: 0 4px;
      opacity: 0.7;
      font-weight: 400;
    }

    .status-tag.hierarchical-status .sub-text {
      font-weight: 400;
      opacity: 0.8;
      font-size: 0.9em;
    }

    /* 订单类型标签 */
    .order-type-tag {
      display: inline-block;
      padding: 4px 10px;
      border-radius: 14px;
      font-size: 11px;
      font-weight: bold;
      color: white;
      text-shadow: 0 1px 1px rgba(0,0,0,0.2);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .order-type-tag.zsb-order {
      background: linear-gradient(135deg, #07c160, #06a552) !important;
      box-shadow: 0 2px 4px rgba(7,193,96,0.3) !important;
    }

    .order-type-tag.station-order {
      background: linear-gradient(135deg, #007aff, #0056cc) !important;
      box-shadow: 0 2px 4px rgba(0,122,255,0.3) !important;
    }

    /* 移动端响应式优化 */
    @media (max-width: 480px) {
      .order-item {
        margin: 12px 0;
        padding: 14px;
        border-radius: 10px;
      }

      .order-header {
        /* 保持桌面端的两列布局 */
      }

      .header-right {
        /* 保持右对齐，但减少左边距 */
        margin-left: 8px;
      }

      .basic-info {
        /* 保持桌面端的两列布局 */
        gap: 8px;
      }

      .left-info {
        flex: 1;
        min-width: 0; /* 允许收缩 */
      }

      .right-info {
        /* 移动端也保持左对齐 */
        min-width: 120px;
        flex: 0 0 auto;
        text-align: left;
      }

      .header-right {
        /* 移动端右上角标签保持右对齐 */
        text-align: right !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-end !important;
      }

      .order-title-row {
        /* 在移动端也保持水平布局，但允许换行 */
        flex-wrap: wrap;
        gap: 4px;
      }

      .order-number {
        font-size: 14px;
        margin-right: 0;
        margin-bottom: 4px;
      }

      .status-row {
        /* 保持桌面端的水平布局 */
        gap: 8px;
        flex-wrap: wrap;
      }

      .status-time {
        margin-top: 4px;
      }

      .status-tag.main-status {
        font-size: 12px;
        padding: 5px 8px;
      }

      .status-tag.sub-status {
        font-size: 10px;
        padding: 3px 6px;
      }

      .status-tag.hierarchical-status {
        font-size: 12px;
        padding: 5px 8px;
      }

      .status-tag.hierarchical-status .sub-text {
        font-size: 0.85em;
      }

      .order-type-tag {
        font-size: 10px !important;
        padding: 3px 6px !important;
      }

      .left-info > div,
      .right-info > div {
        margin: 3px 0;
        font-size: 13px;
      }

      .user-info,
      .project-info {
        font-size: 13px !important;
      }

      .price-info {
        font-size: 13px !important;
      }

      .zsb-contact {
        margin-top: 4px !important;
        font-size: 11px !important;
        padding: 3px 6px !important;
      }

      .action-area {
        justify-content: flex-end !important;
      }
    }

    /* 超小屏幕优化 */
    @media (max-width: 360px) {
      .order-item {
        padding: 12px;
        margin: 10px 0;
      }

      .order-number {
        font-size: 13px;
      }

      .user-info,
      .project-info {
        font-size: 12px;
      }

      .price-info {
        font-size: 12px;
      }

      .status-tag {
        font-size: 10px;
        padding: 3px 6px;
      }
    }

    .status-pending {
      background: #e6f3ff;
      color: #007aff;
    }

    .status-paid {
      background: #e6ffe6;
      color: #07c160;
    }

    .status-unpaid {
      background: #ffe6e6;
      color: #ff3b30;
    }

    .status-completed {
      background: #e6ffe6;
      color: #07c160;
    }

    .status-processing {
      background: #fff7e6;
      color: #ff9500;
    }

    /* 弹窗表单样式 */
    .training-form {
      padding: 16px;
      background-color: #fff;
      max-height: 80vh;
      overflow-y: auto;
    }

    .form-item {
      margin-bottom: 16px;
    }

    .form-item label {
      display: block;
      margin-bottom: 6px;
      font-weight: bold;
      color: #333;
      font-size: 14px;
    }

    /* 收益计算区域样式 */
    .profit-calculation {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 16px;
    }

    .profit-calculation h4 {
      margin: 0 0 10px 0;
      font-size: 14px;
      color: #495057;
      font-weight: bold;
    }

    .formula-item {
      margin-bottom: 8px;
      font-size: 12px;
      color: #6c757d;
      line-height: 1.4;
    }

    .profit-details {
      background: #fff;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 10px;
      margin-top: 10px;
    }

    .profit-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;
      font-size: 13px;
    }

    .profit-row:last-child {
      margin-bottom: 0;
      font-weight: bold;
      color: #28a745;
      border-top: 1px solid #dee2e6;
      padding-top: 6px;
    }

    .profit-label {
      color: #495057;
    }

    .profit-value {
      font-weight: 500;
    }

    /* 价格区间样式 */
    .price-range {
      background: #e3f2fd;
      border: 1px solid #bbdefb;
      border-radius: 6px;
      padding: 8px 12px;
      font-size: 13px;
      color: #1976d2;
      font-weight: 500;
    }

    /* 基准成本价显示 */
    .base-cost-info {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 6px;
      padding: 8px 12px;
      font-size: 13px;
      color: #856404;
      margin-bottom: 10px;
    }

    /* 价格输入框组样式 */
    .price-input-group {
      position: relative;
      display: flex;
      flex-direction: column;
    }

    .price-format-hint {
      font-size: 11px;
      color: #6c757d;
      margin-top: 4px;
      line-height: 1.3;
    }

    .price-format-hint.active {
      color: #007bff;
      font-weight: 500;
    }

    .formatted-result {
      font-size: 12px;
      color: #28a745;
      margin-top: 4px;
      font-weight: 500;
      display: none;
    }

    .formatted-result.warning {
      color: #dc3545;
    }

    /* 响应式优化 */
    @media (max-width: 768px) {
      .training-form {
        padding: 12px;
      }

      .profit-calculation {
        padding: 10px;
      }

      .form-item label {
        font-size: 13px;
      }

      .formula-item {
        font-size: 11px;
      }

      .profit-row {
        font-size: 12px;
      }
    }

    .form-select {
      width: 100%;
      height: 44px;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 0 12px;
      background-color: #fff;
      font-size: 15px;
      color: #333;
      box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
    }

    .form-select:focus {
      border-color: #07c160;
      outline: none;
      box-shadow: 0 0 0 2px rgba(7,193,96,0.2);
    }

    /* Layer弹窗样式覆盖 */
    .layui-layer-molv .layui-layer-title {
      background-color: #07c160 !important;
      color: #fff;
      border: none;
      height: 50px;
      line-height: 50px;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }

    .layui-layer-molv .layui-layer-btn a {
      border-radius: 4px;
      height: 38px;
      line-height: 38px;
      padding: 0 18px;
      font-size: 15px;
    }

    .layui-layer-molv .layui-layer-btn .layui-layer-btn0 {
      border-color: #07c160 !important;
      background-color: #07c160 !important;
      color: #fff;
    }

    .layui-layer-molv .layui-layer-btn .layui-layer-btn1 {
      border-color: #ddd;
      background-color: #f7f7f7;
      color: #333;
    }

    /* 订单类型筛选样式 */
    .order-type-filter {
      border: 1px solid #e5e5e5;
    }

    .filter-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .filter-btn {
      padding: 6px 12px;
      border: 1px solid #ddd;
      border-radius: 15px;
      background: #fff;
      color: #666;
      font-size: 13px;
      cursor: pointer;
      transition: all 0.2s;
      outline: none;
    }

    .filter-btn:hover {
      border-color: #07c160;
      color: #07c160;
    }

    .filter-btn.active {
      background: #07c160;
      border-color: #07c160;
      color: #fff;
    }

    .filter-btn.active:hover {
      background: #06a552;
      border-color: #06a552;
    }
  </style>
  <body>
    <include file="Index/headers"/>

    <!-- 招就办和服务站差异化显示 -->
    <!-- 新的二级状态标签（统一显示） -->
    <div class="tabs-hd02" style="margin: 10px">
      <div class="weap">
        <ul>
          <li class="{:empty($_GET['main_status']) ? 'on' : ''}" data-type="">
            <span class="a">全部订单</span>
          </li>
          <li class="{:$_GET['main_status'] == 'communication' ? 'on' : ''}" data-type="communication">
            <span class="a">沟通流程</span>
          </li>
          <li class="{:$_GET['main_status'] == 'training' ? 'on' : ''}" data-type="training">
            <span class="a">培训流程</span>
          </li>
          <li class="{:$_GET['main_status'] == 'employment' ? 'on' : ''}" data-type="employment">
            <span class="a">入职流程</span>
          </li>
          <li class="{:$_GET['main_status'] == 'service_review' ? 'on' : ''}" data-type="service_review">
            <span class="a">服务审查</span>
          </li>
        </ul>
      </div>
    </div>

    <div class="container">
      <!-- 服务站用户显示订单类型筛选 -->
      <php>if($userRow['zsb_type'] != 2) {</php>
      <div class="order-type-filter" style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 8px;">
        <div class="filter-title" style="font-size: 14px; color: #666; margin-bottom: 8px;">订单类型筛选：</div>
        <div class="filter-buttons">
          <button class="filter-btn {:empty($_GET['order_type']) ? 'active' : ''}" data-order-type="">
            全部订单
          </button>
          <button class="filter-btn {:$_GET['order_type'] == 'station' ? 'active' : ''}" data-order-type="station">
            自有订单
          </button>
          <button class="filter-btn {:$_GET['order_type'] == 'zsb' ? 'active' : ''}" data-order-type="zsb">
            招就办订单
          </button>
        </div>
      </div>
      <php>}</php>

      <div class="search-bar">
        <!-- 移除详细状态筛选，station模块只显示一级状态 -->
        <input
          type="text"
          name="kwd"
          value="{:$_GET['kwd']}"
          class="search-input js_kwd"
          placeholder="搜索学员姓名"
        />
      </div>

      <php>if(empty($list)) {</php>
      <div class="bd">
        <ul>
          <li
            style="
              text-align: center;
              font-size: 13px;
              color: darkgray;
              padding: 20px 0;
            "
          >
            暂无培训订单数据
          </li>
        </ul>
      </div>
      <php>} else {</php>
      <div id="orderList">
        <php>foreach($list as $v) {</php>
        <div class="order-item">
          <!-- 订单头部 - 两列布局 -->
          <div class="order-header">
            <div class="header-left">
              <div class="order-title-row">
                <div class="order-number">订单号：{$v.id}</div>
              </div>
              <div class="order-time">{:date('Y-m-d H:i', $v['create_time'])}</div>
            </div>
            <div class="header-right">
              <!-- 订单类型标识移到右上角（只对服务站用户显示） -->
              <php>if($userRow['zsb_type'] != 2) {</php>
              <php>if($v['is_zsb_order']) {</php>
              <span class="order-type-tag zsb-order">招就办订单</span>
              <php>} else {</php>
              <span class="order-type-tag station-order">自有订单</span>
              <php>}</php>
              <php>}</php>
              <!-- 招就办联系人信息移到订单标签下方（只对服务站用户显示） -->
              <php>if($userRow['zsb_type'] != 2 && $v['is_zsb_order'] && !empty($v['zsb_name'])) {</php>
              <div class="zsb-contact">来自：招就办-{$v.zsb_name}</div>
              <php>}</php>
            </div>
          </div>

          <!-- 主要信息 - 两列布局 -->
          <div class="basic-info">
            <div class="left-info">
              <div class="user-info">学员：{$v.user_name} ({$v.user_mobile})</div>
              <div class="project-info">项目：{$v.project_name}</div>
              <div class="project-info">岗位：{$v.post_name}</div>
            </div>
            <div class="right-info">
              <!-- 根据订单类型显示不同的价格信息 -->
              <php>if($v['is_zsb_order']) {</php>
              <!-- 招就办订单价格信息 -->
              <php>if($userRow['zsb_type'] == 2) {</php>
              <!-- 招就办用户看到的是报名费 -->
              <div class="price-info">培训服务费：
                <span class="price-amount price-negative">{$v.zsb_price_yuan}</span> 元
              </div>
              <div class="price-info">招就办奖励：<span class="price-amount price-positive">{$v.zsb_commission_yuan}</span> 元</div>
              <php>} else {</php>
              <!-- 服务站用户看到的是招就办价格和收益 -->
              <div class="price-info">招就办价格：
                <span class="price-amount price-negative">{$v.zsb_price_yuan}</span> 元
              </div>
              <div class="price-info">服务站入账：<span class="price-amount price-positive">{:number_format($v['station_profit'] / 100, 2)}</span> 元</div>
              <php>}</php>
              <php>} else {</php>
              <!-- 自有订单价格信息 -->
              <div class="price-info">培训服务费：
                <span class="price-amount price-negative">{:number_format($v['fee_amount'] / 100, 2)}</span> 元
                <php>if(!empty($v['fee_text'])) {</php>
                ({$v['fee_text']})
                <php>}</php>

                <php>if(!empty($v['max_fee_amount']) && $v['max_fee_amount'] > 0) {</php>
                ~ <span class="price-amount price-negative">{:number_format($v['max_fee_amount'], 2)}</span> 元
                <php>if(!empty($v['max_fee_text'])) {</php>
                ({$v['max_fee_text']})
                <php>}</php>
                <php>}</php>
              </div>
              <div class="price-info">奖励：<span class="price-amount price-positive">{:number_format($v['reward_station_amt'] / 100, 2)}</span> 元</div>
              <php>}</php>
            </div>
          </div>

          <div class="position-info">
            <div class="status-row">
              <span class="status-tag hierarchical-status status-{$v.main_status_style}">
                <i class="{$v.main_status_icon}"></i>
                <span class="main-text">{$v.main_status_text}</span>
                <!-- 服务审查阶段显示具体结果：服务完成或服务终止 -->
                <php>if($v['main_status'] == 'service_review' && in_array($v['sub_status'], ['completed', 'terminated'])) {</php>
                <span class="separator"> - </span>
                <span class="sub-text">{$v.sub_status_text}</span>
                <php>}</php>
              </span>
            </div>
            <php>if(!empty($v['status_update_time'])) {</php>
            <div class="status-time">
              状态更新：{:date('m-d H:i', $v['status_update_time'])}
            </div>
            <php>}</php>
          </div>

          <div class="action-area">
            <a href="{:U('training/detail', ['id' => $v['id']])}" class="btn btn-primary">
              查看详情
            </a>
          </div>
        </div>
        <php>}</php>
      </div>
      <div class="pagination-container">
        <div class="pagination-wrapper">
          {$page}
        </div>
        <div class="page-size-selector">
          <span>每页显示:</span>
          <select id="pageSizeSelector">
            <option value="10" <php>if($pageSize == 10) echo 'selected';</php>>10条</option>
            <option value="20" <php>if($pageSize == 20) echo 'selected';</php>>20条</option>
            <option value="50" <php>if($pageSize == 50) echo 'selected';</php>>50条</option>
          </select>
        </div>
      </div>

      <style>
        /* 分页样式优化 */
        .pagination-container {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 20px 0;
          padding: 0 10px;
        }

        .pagination-wrapper {
          flex: 1;
        }

        .pagination-wrapper div {
          margin: 0 !important;
        }

        .pagination-wrapper ul.pagination {
          display: flex;
          justify-content: center;
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .pagination-wrapper li {
          margin: 0 3px;
        }

        .pagination-wrapper li a,
        .pagination-wrapper li span {
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 36px;
          height: 36px;
          padding: 0 10px;
          border-radius: 18px;
          background-color: #f5f5f5;
          color: #333;
          text-decoration: none;
          font-size: 14px;
          transition: all 0.2s;
        }

        .pagination-wrapper li a:hover {
          background-color: #e0e0e0;
        }

        .pagination-wrapper li.active span {
          background-color: #07c160;
          color: white;
        }

        .pagination-wrapper li.disabled a,
        .pagination-wrapper li.disabled span {
          color: #bbb;
          cursor: not-allowed;
          background-color: #f8f8f8;
        }

        .page-size-selector {
          display: flex;
          align-items: center;
          margin-left: 15px;
        }

        .page-size-selector span {
          color: #666;
          margin-right: 8px;
          font-size: 14px;
        }

        .page-size-selector select {
          height: 36px;
          border: 1px solid #ddd;
          border-radius: 18px;
          padding: 0 12px;
          background-color: #f5f5f5;
          color: #333;
          font-size: 14px;
          outline: none;
          transition: all 0.2s;
        }

        .page-size-selector select:focus {
          border-color: #07c160;
          box-shadow: 0 0 0 2px rgba(7,193,96,0.2);
        }

        @media (max-width: 768px) {
          .pagination-container {
            flex-direction: column;
            gap: 15px;
          }

          .page-size-selector {
            margin-left: 0;
          }
        }
      </style>
      <php>}</php>
    </div>

    <!--div class="footernav">
      <div class="box">
        <ul>
          <li>
            <php>if($userRow['zsb_type'] == 2) {</php>
            <button class="add-btn" id="createTrainingBtn">+ 报名培训项目</button>
            <php>} else {</php>
            <button class="add-btn" id="createTrainingBtn">+ 创建培训订单</button>
            <php>}</php>
          </li>
        </ul>
      </div>
    </div-->

    <!-- 创建培训订单弹窗 -->
    <div id="createTrainingModal" style="display:none;">
      <div class="training-form">
        <form id="createTrainingForm" action="/training/create" method="post">
          <div class="form-item">
            <label><i class="iconfont icon-user" style="margin-right: 5px;"></i>学员：</label>
            <select name="user_id" id="user_id" class="form-select" required>
              <option value="">请选择学员</option>
            </select>
          </div>
          <div class="form-item">
            <label><i class="iconfont icon-project" style="margin-right: 5px;"></i>培训项目：</label>
            <select name="project_id" id="project_id" class="form-select" required>
              <option value="">请选择培训项目</option>
            </select>
          </div>
          <div class="form-item">
            <label><i class="iconfont icon-tag" style="margin-right: 5px;"></i>培训岗位：</label>
            <select name="post_id" id="post_id" class="form-select" required>
              <option value="">请先选择培训项目</option>
            </select>
          </div>
          <div class="form-item">
            <label><i class="iconfont icon-money" style="margin-right: 5px;"></i>培训服务费：</label>
            <p id="fee_amount" style="color: #ff6b35; font-weight: bold; font-size: 16px; padding: 8px 0;">0.00 元</p>
          </div>
        </form>
      </div>
    </div>

    <!-- 预先生成URL -->
    <script type="text/javascript">
      // 定义API URL
      var API_URLS = {
        getFormData: "{:U('training/getFormData')}",
        getProjectPosts: "{:U('training/getProjectPosts')}",
        create: "{:U('training/create')}"
      };

      // 定义页面URL
      var trainingIndexUrl = "{:U('training/index')}";

      // 全局价格格式化函数
      function formatMoney(amount) {
        return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      }

      function formatPriceToThousands(value) {
        if (!value || value === '') {
          return 0;
        }
        var num = parseInt(value);
        if (isNaN(num) || num <= 0) {
          return 0;
        }
        // 向下取整到千位（保留千位以上的数字，去除百位、十位、个位）
        return Math.floor(num / 1000) * 1000;
      }

      function showFormattedPriceHint(inputElement, originalValue, formattedValue) {
        var $inputGroup = $(inputElement).closest('.price-input-group');
        var $hint = $inputGroup.find('.price-format-hint');
        var $result = $inputGroup.find('.formatted-result');

        if (originalValue != formattedValue) {
          $hint.addClass('active');
          $result.text('格式化后：' + formatMoney(formattedValue) + ' 元').show();
          if (formattedValue === 0) {
            $result.addClass('warning').text('格式化后：0 元（原价格小于1000）');
          } else {
            $result.removeClass('warning');
          }
        } else {
          $hint.removeClass('active');
          $result.hide();
        }
      }

      function validateTrainingFeeWithFormatting(input) {
        var value = input.value;

        // 移除千分符和非数字字符，只保留数字
        var numericValue = value.replace(/[^\d]/g, '');

        if (numericValue === '' || numericValue === '0') {
          input.value = '';
          // 隐藏格式化提示
          var $inputGroup = $(input).closest('.price-input-group');
          $inputGroup.find('.price-format-hint').removeClass('active');
          $inputGroup.find('.formatted-result').hide();
          // 清空收益显示
          $('#profit_fee_amount').text('0.00 元');
          $('#profit_base_cost').text('0.00 元');
          $('#profit_platform_fee').text('0.00 元');
          $('#profit_station_profit').text('0.00 元');
          return;
        }

        // 移除前导零
        numericValue = numericValue.replace(/^0+/, '');
        if (numericValue === '') {
          input.value = '';
          return;
        }

        // 转换为整数
        var intValue = parseInt(numericValue);
        if (isNaN(intValue) || intValue <= 0) {
          input.setCustomValidity('请输入正整数');
          input.style.borderColor = '#dc3545';
          return;
        }

        // 实时添加千分符显示
        var formattedDisplay = formatMoney(intValue);

        // 保存光标位置
        var cursorPosition = input.selectionStart;
        var oldLength = input.value.length;

        // 更新输入框值为千分符格式
        input.value = formattedDisplay;

        // 恢复光标位置（考虑千分符的影响）
        var newLength = input.value.length;
        var lengthDiff = newLength - oldLength;
        var newCursorPosition = cursorPosition + lengthDiff;

        // 确保光标位置在有效范围内
        if (newCursorPosition < 0) newCursorPosition = 0;
        if (newCursorPosition > newLength) newCursorPosition = newLength;

        // 设置光标位置
        setTimeout(function() {
          input.setSelectionRange(newCursorPosition, newCursorPosition);
        }, 0);

        // 显示格式化预览
        var formattedValue = formatPriceToThousands(intValue);
        showFormattedPriceHint(input, intValue, formattedValue);

        // 验证费用范围
        var minFee = parseFloat($(input).attr('min')) || 0;
        var maxFee = parseFloat($(input).attr('max')) || 0;

        if (minFee > 0 && formattedValue < minFee) {
          input.setCustomValidity('格式化后的培训服务费不得低于最低报价（' + formatMoney(minFee) + '元）');
          input.style.borderColor = '#dc3545';
          return;
        }

        if (maxFee > 0 && formattedValue > maxFee) {
          input.setCustomValidity('格式化后的培训服务费不得超过最高报价（' + formatMoney(maxFee) + '元）');
          input.style.borderColor = '#dc3545';
          return;
        }

        input.setCustomValidity('');
        input.style.borderColor = '#28a745';

        // 实时计算收益（使用格式化后的值）
        var postId = $('#modal_post_id').val();
        if (postId && formattedValue > 0) {
          calculateProfit(postId, formattedValue);
        }
      }

      function applyTrainingFeeFormatting(input) {
        if (input.value && input.value !== '') {
          var originalValue = parseInt(input.value.replace(/[^\d]/g, '')) || 0;
          var formattedValue = formatPriceToThousands(originalValue);

          if (formattedValue > 0) {
            input.value = formatMoney(formattedValue);
          } else {
            input.value = '';
          }

          // 重新触发验证
          validateTrainingFeeWithFormatting(input);
        }
      }

      // 收益计算函数
      function calculateProfit(postId, feeAmount) {
        $.ajax({
          url: "{:U('training/getPostProfitDetails')}",
          type: 'GET',
          data: {
            post_id: postId,
            fee_amount: feeAmount
          },
          dataType: 'json',
          success: function(res) {
            if (res.status == 1 && res.data.profit_details) {
              var profit = res.data.profit_details;
              $('#profit_fee_amount').text(formatMoney(profit.fee_amount) + ' 元');
              $('#profit_base_cost').text(formatMoney(profit.base_cost) + ' 元');
              $('#profit_platform_fee').text(formatMoney(profit.platform_fee) + ' 元');
              $('#profit_station_profit').text(formatMoney(profit.station_profit) + ' 元');
            }
          },
          error: function() {
            console.log('计算收益失败');
          }
        });
      }

      $(document).ready(function(){
        // 订单类型筛选按钮点击事件
        $('.filter-btn').click(function(){
          $(this).siblings().removeClass('active');
          $(this).addClass('active');
          var orderType = $(this).attr('data-order-type');
          var mainStatus = $(".tabs-hd02 ul li.on").attr('data-type');
          var subStatus = $('.js_sub_status').val();
          var kwd = $('.js_kwd').val();
          var pageSize = $('#pageSizeSelector').val();

          var url = trainingIndexUrl;
          var params = [];

          if(orderType) params.push("order_type=" + orderType);
          if(mainStatus) params.push("main_status=" + mainStatus);
          if(subStatus) params.push("sub_status=" + subStatus);
          if(kwd) params.push("kwd=" + kwd);
          if(pageSize) params.push("psz=" + pageSize);

          if(params.length > 0) {
            url += "?" + params.join("&");
          }

          window.location.href = url;
        });

        // 标签切换
        $(".tabs-hd02 ul li").click(function(){
          $(this).siblings().removeClass("on");
          $(this).addClass("on");
          var mainStatus = $(this).attr('data-type');
          var orderType = $('.filter-btn.active').attr('data-order-type');
          var subStatus = $('.js_sub_status').val();
          var kwd = $('.js_kwd').val();
          var pageSize = $('#pageSizeSelector').val();

          var url = trainingIndexUrl;
          var params = [];

          if(orderType) params.push("order_type=" + orderType);
          if(mainStatus) params.push("main_status=" + mainStatus);
          if(subStatus) params.push("sub_status=" + subStatus);
          if(kwd) params.push("kwd=" + kwd);
          if(pageSize) params.push("psz=" + pageSize);

          if(params.length > 0) {
            url += "?" + params.join("&");
          }

          window.location.href = url;
        });

        // 移除详细状态筛选功能，station模块只显示一级状态

        // 关键词搜索
        $('.js_kwd').blur(function(){
          var orderType = $('.filter-btn.active').attr('data-order-type');
          var mainStatus = $(".tabs-hd02 ul li.on").attr('data-type');
          var kwd = $(this).val();
          var pageSize = $('#pageSizeSelector').val();

          var url = trainingIndexUrl;
          var params = [];

          if(orderType) params.push("order_type=" + orderType);
          if(mainStatus) params.push("main_status=" + mainStatus);
          if(kwd) params.push("kwd=" + kwd);
          if(pageSize) params.push("psz=" + pageSize);

          if(params.length > 0) {
            url += "?" + params.join("&");
          }

          window.location.href = url;
        });

        // 每页显示数量变更
        $('#pageSizeSelector').change(function() {
          var orderType = $('.filter-btn.active').attr('data-order-type');
          var mainStatus = $(".tabs-hd02 ul li.on").attr('data-type');
          var kwd = $('.js_kwd').val();
          var pageSize = $(this).val();

          var url = trainingIndexUrl;
          var params = [];

          if(orderType) params.push("order_type=" + orderType);
          if(mainStatus) params.push("main_status=" + mainStatus);
          if(kwd) params.push("kwd=" + kwd);
          if(pageSize) params.push("psz=" + pageSize);

          if(params.length > 0) {
            url += "?" + params.join("&");
          }

          window.location.href = url;
        });

        // 全局变量存储表单数据
        var globalFormData = null;

        // 创建培训订单按钮点击事件
        $('#createTrainingBtn').click(function() {
          // 先通过AJAX获取表单数据
          $.ajax({
            url: API_URLS.getFormData,
            type: 'GET',
            dataType: 'json',
            success: function(res) {
              if (res.status == 1) {
                console.log('获取表单数据成功:', res.data);

                // 存储到全局变量
                globalFormData = res.data;

                // 创建弹窗内容
                var modalContent = '<div class="training-form" style="padding: 20px; background-color: #fff;">';
                modalContent += '<form id="trainingForm">';

                // 学员选择
                modalContent += '<div class="form-item" style="margin-bottom: 20px;">';
                modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="iconfont icon-user" style="margin-right: 5px;"></i>学员：</label>';
                modalContent += '<select name="user_id" id="modal_user_id" class="form-select" style="width: 100%; height: 44px; border: 1px solid #ddd; border-radius: 5px; padding: 0 12px; background-color: #fff; font-size: 15px; color: #333; box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);" required>';
                modalContent += '<option value="">请选择学员</option>';

                // 填充学员选项 - 从简历获取
                $.each(res.data.users, function(index, user) {
                  var stateText = user.job_state_text ? ' - ' + user.job_state_text : '';
                  var resumeTypeText = user.resume_type_text ? ' [' + user.resume_type_text + ']' : '';
                  modalContent += '<option value="' + user.id + '" data-resume-type="' + (user.resume_type || 1) + '" data-service-station-id="' + (user.service_station_id || '') + '">' + user.realname + ' (' + user.mobile + ')' + stateText + resumeTypeText + '</option>';
                });

                modalContent += '</select>';
                modalContent += '</div>';

                // 培训项目选择
                modalContent += '<div class="form-item" style="margin-bottom: 20px;">';
                modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="iconfont icon-project" style="margin-right: 5px;"></i>培训项目：</label>';
                modalContent += '<select name="project_id" id="modal_project_id" class="form-select" style="width: 100%; height: 44px; border: 1px solid #ddd; border-radius: 5px; padding: 0 12px; background-color: #fff; font-size: 15px; color: #333; box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);" required>';
                modalContent += '<option value="">请选择培训项目</option>';

                // 填充项目选项
                $.each(res.data.projects, function(index, project) {
                  modalContent += '<option value="' + project.id + '">' + project.name + '</option>';
                });

                modalContent += '</select>';
                modalContent += '</div>';

                // 培训岗位选择
                modalContent += '<div class="form-item" style="margin-bottom: 20px;">';
                modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="iconfont icon-tag" style="margin-right: 5px;"></i>培训岗位：</label>';
                modalContent += '<select name="post_id" id="modal_post_id" class="form-select" style="width: 100%; height: 44px; border: 1px solid #ddd; border-radius: 5px; padding: 0 12px; background-color: #fff; font-size: 15px; color: #333; box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);" required>';
                modalContent += '<option value="">请先选择培训项目</option>';
                modalContent += '</select>';
                modalContent += '</div>';

                // 学员学历和学员专业字段已隐藏

                // 岗位价格区间显示（服务站价格界面）
                modalContent += '<div class="form-item station-price-section">';
                modalContent += '<label><i class="iconfont icon-money" style="margin-right: 5px;"></i>岗位价格区间：</label>';
                modalContent += '<div id="modal_price_range" class="price-range">请先选择培训岗位</div>';
                modalContent += '</div>';

                // 基准成本价显示（服务站价格界面）
                modalContent += '<div class="form-item station-price-section">';
                modalContent += '<div id="modal_base_cost_info" class="base-cost-info" style="display: none;">';
                modalContent += '<strong>基准成本价：</strong><span id="modal_base_cost">0</span> 元';
                modalContent += '</div>';
                modalContent += '</div>';

                // 招就办价格界面（始终生成，通过CSS类控制显示）
                modalContent += '<div class="form-item zsb-price-section" id="zsb_fee_display_section" style="display: none;">';
                modalContent += '<label><i class="iconfont icon-check" style="margin-right: 5px;"></i>培训服务费：</label>';
                modalContent += '<div class="zsb-fee-display" style="padding: 12px; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; font-weight: bold; color: #07c160;">';
                modalContent += '<span id="zsb_fee_amount">请先选择培训岗位</span>';
                modalContent += '</div>';
                modalContent += '<div style="font-size: 12px; color: #666; margin-top: 5px;">培训服务费由招就办价格配置自动确定</div>';
                modalContent += '</div>';

                // 服务站价格输入界面（始终生成，通过CSS类控制显示）
                modalContent += '<div class="form-item station-price-section" id="fee_input_section">';
                modalContent += '<label><i class="iconfont icon-check" style="margin-right: 5px;"></i>输入培训服务费（合同金额）：</label>';
                modalContent += '<div class="price-input-group">';
                modalContent += '<input type="text" id="modal_fee_input" class="form-control" placeholder="请输入培训服务费（元）" style="width: 100%; height: 40px; border: 1px solid #ddd; border-radius: 5px; padding: 0 12px; background-color: #fff; font-size: 14px; color: #333;">';
                modalContent += '<div class="price-format-hint">价格将自动调整为千的整数倍（如：12345 → 12000）</div>';
                modalContent += '<div class="formatted-result" style="display: none;"></div>';
                modalContent += '<div id="fee_range_hint" style="font-size: 12px; color: #666; margin-top: 5px;">请先选择培训岗位</div>';
                modalContent += '</div>';
                modalContent += '</div>';

                // 招就办收益显示（始终生成，通过CSS类控制显示）
                modalContent += '<div class="form-item zsb-price-section" id="modal_zsb_profit" style="display: none;">';
                modalContent += '<label><i class="iconfont icon-check" style="margin-right: 5px;"></i>招就办收益：</label>';
                modalContent += '<div class="zsb-commission-display" style="padding: 12px; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; font-weight: bold; color: #07c160;">';
                modalContent += '<span id="zsb_commission_amount">0.00 元</span>';
                modalContent += '</div>';
                modalContent += '</div>';

                // 服务站收益计算显示（始终生成，通过CSS类控制显示）
                modalContent += '<div class="profit-calculation station-price-section" id="modal_profit_calculation" style="display: none;">';
                modalContent += '<h4>收益计算</h4>';
                modalContent += '<div class="formula-item">平台服务费 = max(0, (培训服务费 - 最低报价) × <span id="modal_platform_rate">30</span>%)</div>';
                modalContent += '<div class="formula-item">服务站收益 = 培训服务费 - 基准成本价 - 平台服务费</div>';
                modalContent += '<div class="profit-details" id="modal_profit_details">';
                modalContent += '<div class="profit-row"><span class="profit-label">培训服务费：</span><span class="profit-value" id="profit_fee_amount">0.00 元</span></div>';
                modalContent += '<div class="profit-row"><span class="profit-label">基准成本价：</span><span class="profit-value" id="profit_base_cost">0.00 元</span></div>';
                modalContent += '<div class="profit-row"><span class="profit-label">平台服务费：</span><span class="profit-value" id="profit_platform_fee">0.00 元</span></div>';
                modalContent += '<div class="profit-row"><span class="profit-label">服务站收益：</span><span class="profit-value" id="profit_station_profit">0.00 元</span></div>';
                modalContent += '</div>';
                modalContent += '</div>';

                modalContent += '</form>';
                modalContent += '</div>';

                // 打开弹窗
                layer.open({
                  type: 1,
                  title: '<span style="font-weight: bold; font-size: 16px;">创建培训订单</span>',
                  area: ['480px', 'auto'], // 固定宽度，避免超宽问题
                  maxHeight: '80vh', // 限制最大高度
                  skin: 'layui-layer-molv', // 使用墨绿色皮肤
                  content: modalContent, // 直接使用我们构建的HTML内容
                  btn: ['提交', '取消'],
                  btnAlign: 'c', // 按钮居中
                  success: function(layero, index) {
                    // 添加样式到弹窗
                    $(layero).find('.layui-layer-title').css({
                      'background-color': '#07c160',
                      'color': '#fff',
                      'border': 'none',
                      'height': '50px',
                      'line-height': '50px',
                      'display': 'flex',
                      'align-items': 'center',
                      'justify-content': 'center'
                    });

                    $(layero).find('.layui-layer-btn a').css({
                      'border-radius': '4px',
                      'height': '38px',
                      'line-height': '38px',
                      'padding': '0 18px',
                      'font-size': '15px'
                    });

                    $(layero).find('.layui-layer-btn .layui-layer-btn0').css({
                      'border-color': '#07c160',
                      'background-color': '#07c160',
                      'color': '#fff'
                    });

                    $(layero).find('.layui-layer-btn .layui-layer-btn1').css({
                      'border-color': '#ddd',
                      'background-color': '#f7f7f7',
                      'color': '#333'
                    });

                    console.log('弹窗创建成功');

                    // 获取弹窗中的元素
                    var $modalUserSelect = $('#modal_user_id');
                    var $modalProjectSelect = $('#modal_project_id');
                    var $modalPostSelect = $('#modal_post_id');
                    var $modalFeeElement = $('#modal_fee_amount');
                    var $modalFeeInput = $('#modal_fee_input');
                    var $feeRangeHint = $('#fee_range_hint');
                    // 学员学历和专业相关变量已移除

                    console.log('弹窗元素初始化完成');

                    // 学员学历和专业信息获取逻辑已移除

                    // 学员选择变化事件 - 根据简历类型动态切换界面
                    $modalUserSelect.on('change', function() {
                      var selectedOption = $(this).find('option:selected');
                      var resumeType = selectedOption.attr('data-resume-type') || '1';
                      var serviceStationId = selectedOption.attr('data-service-station-id') || '';

                      console.log('学员选择变化，简历类型:', resumeType, '服务站ID:', serviceStationId);

                      // 根据简历类型动态切换界面
                      if (resumeType == '2') {
                        // 招就办简历：显示招就办价格界面
                        console.log('显示招就办价格界面');
                        $('.zsb-price-section').show();
                        $('.station-price-section').hide();

                        // 重置招就办价格显示
                        $('#zsb_fee_amount').text('请先选择培训岗位');
                        $('#zsb_fee_display_section').hide();
                        $('#modal_zsb_profit').hide();
                        $('#modal_fee_input').val('');
                      } else {
                        // 自有简历：显示普通价格输入界面
                        console.log('显示普通价格输入界面');
                        $('.zsb-price-section').hide();
                        $('.station-price-section').show();

                        // 重置普通价格显示
                        $('#modal_price_range').text('请先选择培训岗位');
                        $('#modal_base_cost_info').hide();
                        $('#modal_profit_calculation').hide();
                        $('#modal_fee_input').val('');
                        $('#fee_range_hint').text('请先选择培训岗位');
                      }

                      // 重置项目和岗位选择
                      $modalProjectSelect.val('').trigger('change');
                    });

                    // 项目选择变化事件
                    $modalProjectSelect.on('change', function() {
                      var projectId = $(this).val();
                      console.log('项目选择变化，ID:', projectId);

                      if (projectId) {
                        $.ajax({
                          url: API_URLS.getProjectPosts,
                          type: 'GET',
                          data: {project_id: projectId},
                          dataType: 'json',
                          success: function(res) {
                            console.log('获取岗位数据成功:', res);
                            if (res.status == 1 && res.data && res.data.length > 0) {
                              var posts = res.data;
                              var options = '<option value="">请选择培训岗位</option>';

                              for (var i = 0; i < posts.length; i++) {
                                options += '<option value="' + posts[i].id + '" ' +
                                  'data-service-price="' + posts[i].service_price_formatted + '" ' +
                                  'data-service-price-in-cents="' + posts[i].service_price_in_cents + '" ' +
                                  'data-service-price-text="' + posts[i].service_price_text + '" ' +
                                  'data-max-price="' + posts[i].max_price_formatted + '" ' +
                                  'data-max-price-in-cents="' + posts[i].max_price_in_cents + '" ' +
                                  'data-max-price-text="' + posts[i].max_price_text + '" ' +
                                  'data-price="' + posts[i].service_price_formatted + '" ' +
                                  'data-price-in-cents="' + posts[i].service_price_in_cents + '">' +
                                  posts[i].name + '</option>';
                              }

                              $modalPostSelect.html(options);
                              console.log('岗位下拉菜单已更新');
                            } else {
                              $modalPostSelect.html('<option value="">无</option>');
                              showMessage('没有找到相关岗位', 2);
                            }
                          },
                          error: function() {
                            showMessage('获取岗位列表失败', 2);
                          }
                        });
                      } else {
                        $modalPostSelect.html('<option value="">请先选择培训项目</option>');
                        // 重置所有价格显示
                        $('#modal_price_range').text('请先选择培训项目');
                        $('#modal_base_cost_info').hide();
                        $('#modal_profit_calculation').hide();
                        $('#modal_fee_input').val('');
                        $('#fee_range_hint').text('请先选择培训项目');
                        $('#zsb_fee_amount').text('请先选择培训项目');
                        $('#zsb_fee_display_section').hide();
                        $('#modal_zsb_profit').hide();
                      }
                    });

                    // 岗位选择变化事件
                    $modalPostSelect.on('change', function() {
                      var postId = $(this).val(); // 获取选中的岗位ID

                      // 只有选择了岗位才获取详细信息
                      if (postId) {
                        // 检查当前选择的学员类型
                        var selectedUserOption = $modalUserSelect.find('option:selected');
                        var resumeType = selectedUserOption.attr('data-resume-type') || '1';

                        console.log('岗位选择变化，岗位ID:', postId, '简历类型:', resumeType);

                        if (resumeType == '2') {
                          // 招就办简历：获取招就办价格配置
                          console.log('招就办简历调用价格API，岗位ID:', postId);
                          $.ajax({
                            url: "{:U('training/getZsbPostPrice')}",
                            type: 'GET',
                            data: {
                              post_id: postId,
                              zsb_id: selectedUserOption.attr('data-service-station-id') // 传递招就办ID
                            },
                            dataType: 'json',
                            success: function(res) {
                              console.log('招就办价格API调用成功:', res);
                              if (res.status == 1) {
                                var data = res.data;

                                // 显示招就办费用
                                $('#zsb_fee_amount').text(data.sale_price_formatted + ' 元');
                                $('#zsb_fee_display_section').show();

                                // 设置隐藏输入框的值（用于提交）
                                $('#modal_fee_input').val(data.sale_price_formatted);

                                // 显示招就办收益
                                $('#zsb_commission_amount').text(data.commission_formatted + ' 元');
                                $('#modal_zsb_profit').show();

                              } else {
                                $('#zsb_fee_amount').text('未配置价格');
                                $('#zsb_fee_display_section').show();
                                $('#modal_zsb_profit').hide();
                              }
                            },
                            error: function(xhr, status, error) {
                              console.error('获取招就办价格失败:', error);
                              console.error('响应状态:', xhr.status);
                              console.error('响应内容:', xhr.responseText);
                              $('#zsb_fee_amount').text('获取价格失败');
                              $('#zsb_fee_display_section').show();
                              $('#modal_zsb_profit').hide();
                            }
                          });
                        } else if ('{$serviceStationRow.zsb_type}' == '2') {
                          // 招就办用户但选择的是自有简历：获取招就办价格配置
                        console.log('招就办用户调用价格API，岗位ID:', postId);
                        $.ajax({
                          url: "{:U('training/getZsbPostPrice')}",
                          type: 'GET',
                          data: {post_id: postId},
                          dataType: 'json',
                          success: function(res) {
                            console.log('API调用成功:', res);
                            if (res.status == 1) {
                              var data = res.data;

                              // 显示招就办费用
                              $('#zsb_fee_amount').text(data.sale_price_formatted + ' 元');
                              $('#zsb_fee_display_section').show();

                              // 设置隐藏输入框的值（用于提交）
                              $('#modal_fee_input').val(data.sale_price_formatted);

                              // 显示招就办收益
                              $('#zsb_commission_amount').text(data.commission_formatted + ' 元');
                              $('#modal_zsb_profit').show();

                            } else {
                              $('#zsb_fee_amount').text('未配置价格');
                              $('#zsb_fee_display_section').show();
                              $('#modal_zsb_profit').hide();
                            }
                          },
                          error: function(xhr, status, error) {
                            console.error('获取招就办价格失败:', error);
                            console.error('响应状态:', xhr.status);
                            console.error('响应内容:', xhr.responseText);
                            $('#zsb_fee_amount').text('获取价格失败');
                            $('#zsb_fee_display_section').show();
                            $('#modal_zsb_profit').hide();
                          }
                        });
                        } else {
                          // 自有简历或服务站用户：获取岗位收益计算详情
                        $.ajax({
                          url: "{:U('training/getPostProfitDetails')}",
                          type: 'GET',
                          data: {post_id: postId},
                          dataType: 'json',
                          success: function(res) {
                            if (res.status == 1) {
                              var data = res.data;

                              // 更新价格区间显示
                              $('#modal_price_range').text(data.price_range);

                              // 显示基准成本价
                              $('#modal_base_cost').text(data.base_cost);
                              $('#modal_base_cost_info').show();

                              // 更新输入框的范围提示和属性（使用基准成本价作为最低值）
                              $('#modal_fee_input').attr('min', data.min_fee);
                              $('#modal_fee_input').attr('max', data.max_fee);
                              $('#fee_range_hint').text('培训服务费范围：' + data.fee_range);

                              // 更新平台费率显示
                              $('#modal_platform_rate').text(data.platform_rate_percent);

                              // 设置默认值为最低报价（格式化后的值）
                              var formattedMinFee = formatPriceToThousands(data.min_fee);
                              $('#modal_fee_input').val(formatMoney(formattedMinFee));

                              // 显示收益计算区域
                              $('#modal_profit_calculation').show();

                              // 立即计算一次收益（使用格式化后的最低报价）
                              calculateProfit(postId, formattedMinFee);

                            } else {
                              $('#fee_range_hint').text('无法获取岗位信息');
                              $('#modal_profit_calculation').hide();
                              $('#modal_base_cost_info').hide();
                            }
                          },
                          error: function() {
                            $('#fee_range_hint').text('获取岗位信息失败');
                            $('#modal_profit_calculation').hide();
                            $('#modal_base_cost_info').hide();
                          }
                        });
                        }
                      } else {
                        // 没有选择岗位时，清空所有显示
                        $('#zsb_fee_amount').text('请先选择培训岗位');
                        $('#zsb_fee_display_section').hide();
                        $('#modal_zsb_profit').hide();
                        $('#modal_price_range').text('请先选择培训岗位');
                        $('#modal_base_cost_info').hide();
                        $('#modal_profit_calculation').hide();
                        $('#modal_fee_input').val('');
                        $('#fee_range_hint').text('请先选择培训岗位');
                      }
                    });

                    // 报名费输入变化事件 - 实时格式化和计算收益（仅自有简历）
                    $('#modal_fee_input').on('input', function() {
                      // 获取当前选中学员的简历类型
                      var currentUserId = $('#modal_user_id').val();
                      var currentUser = globalFormData.users.find(function(user) {
                        return user.id == currentUserId;
                      });
                      var currentResumeType = currentUser ? currentUser.resume_type : '1';

                      if (currentResumeType != '2') { // 只有自有简历才需要格式化
                        validateTrainingFeeWithFormatting(this);
                      }
                    });

                    // 报名费输入框失去焦点时应用格式化
                    $('#modal_fee_input').on('blur', function() {
                      // 获取当前选中学员的简历类型
                      var currentUserId = $('#modal_user_id').val();
                      var currentUser = globalFormData.users.find(function(user) {
                        return user.id == currentUserId;
                      });
                      var currentResumeType = currentUser ? currentUser.resume_type : '1';

                      if (currentResumeType != '2') { // 只有自有简历才需要格式化
                        applyTrainingFeeFormatting(this);
                      }
                    });


                  },
                  yes: function(index) {
                    try {
                      // 获取表单数据
                      var userJobId = $('#modal_user_id').val();
                      var projectId = $('#modal_project_id').val();
                      var postId = $('#modal_post_id').val();

                      console.log('提交表单，简历ID:', userJobId, '项目ID:', projectId, '岗位ID:', postId);

                      // 表单验证
                      if (!userJobId) {
                        layer.open({
                          content: '请选择学员',
                          skin: 'msg',
                          time: 2
                        });
                        return false;
                      }
                      if (!projectId) {
                        layer.open({
                          content: '请选择培训项目',
                          skin: 'msg',
                          time: 2
                        });
                        return false;
                      }
                      if (!postId) {
                        layer.open({
                          content: '请选择培训岗位',
                          skin: 'msg',
                          time: 2
                        });
                        return false;
                      }

                      // 获取费用金额（根据简历类型处理）
                      var feeAmount;

                      // 重新获取当前选中学员的简历类型
                      var currentUserId = $('#modal_user_id').val();
                      var currentUser = globalFormData.users.find(function(user) {
                        return user.id == currentUserId;
                      });
                      var currentResumeType = currentUser ? currentUser.resume_type : '1';

                      console.log('提交表单，简历ID:', currentUserId, '简历类型:', currentResumeType);

                      if (currentResumeType == '2') {
                        // 招就办简历：直接从隐藏输入框获取价格
                        feeAmount = parseFloat($('#modal_fee_input').val()) || 0;

                        if (!feeAmount || feeAmount <= 0) {
                          layer.open({
                            content: '请先选择培训岗位以获取价格配置',
                            skin: 'msg',
                            time: 2
                          });
                          return false;
                        }
                      } else {
                        // 自有简历：从输入框中获取格式化后的价格
                        var inputValue = $('#modal_fee_input').val().replace(/[^\d]/g, ''); // 移除千分符
                        var originalAmount = parseInt(inputValue) || 0;
                        feeAmount = formatPriceToThousands(originalAmount); // 使用格式化后的值

                        if (!feeAmount || feeAmount <= 0) {
                          layer.open({
                            content: '请输入有效的培训服务费',
                            skin: 'msg',
                            time: 2
                          });
                          return false;
                        }

                        // 验证费用范围（使用格式化后的值）
                        var minFee = parseFloat($('#modal_fee_input').attr('min'));
                        var maxFee = parseFloat($('#modal_fee_input').attr('max'));
                        if (feeAmount < minFee || feeAmount > maxFee) {
                          layer.open({
                            content: '培训服务费必须在 ' + formatMoney(minFee) + ' - ' + formatMoney(maxFee) + ' 元之间（最低报价~最高报价）',
                            skin: 'msg',
                            time: 3
                          });
                          return false;
                        }
                      }
                      console.log('费用金额(分):', feeAmount);

                      // 提交表单
                      $.ajax({
                        url: "{:U('training/create')}", // 使用ThinkPHP U函数生成正确的URL
                        type: 'POST',
                        data: {
                          user_id: userJobId,
                          post_id: postId,
                          fee_amount: feeAmount
                        },
                        dataType: 'json',
                        beforeSend: function() {
                          console.log('正在提交表单数据:', {
                            user_id: userJobId,
                            post_id: postId,
                            fee_amount: feeAmount
                          });
                        },
                        success: function(res) {
                          console.log('表单提交响应:', res);
                          if (res.status == 1) {
                            layer.open({
                              content: '培训报名成功',
                              skin: 'msg',
                              time: 2
                            });
                            setTimeout(function() {
                              window.location.href = res.url || "{:U('training/index')}";
                            }, 1000);
                          } else {
                            layer.open({
                              content: res.info || '培训报名失败',
                              skin: 'msg',
                              time: 2
                            });
                          }
                        },
                        error: function(xhr, status, error) {
                          console.error('表单提交失败:', status, error);
                          console.log('响应文本:', xhr.responseText);
                          layer.open({
                            content: '网络错误，请重试',
                            skin: 'msg',
                            time: 2
                          });
                        }
                      });
                    } catch (e) {
                      console.error('表单提交过程中出错:', e);
                      layer.open({
                        content: '表单提交出错',
                        skin: 'msg',
                        time: 2
                      });
                      return false;
                    }
                  }
                });
              } else {
                layer.msg('获取表单数据失败', {icon: 2});
              }
            },
            error: function() {
              layer.msg('网络错误，请重试', {icon: 2});
            }
          });
        });

        // 事件处理已移至弹窗的success回调中
      });
    </script>

    <!-- 微信分享菜单隐藏功能 - 自动应用 -->
    <if condition="isset($wechatHideShareScript)">
    {$wechatHideShareScript}
    </if>
  </body>
</html>
